const fs = require('fs');
const path = require('path');

const DATA_FILE = path.join(__dirname, '..', 'data', 'players.json');

// Garantir que o diretório data existe
function ensureDataDirectory() {
    const dataDir = path.dirname(DATA_FILE);
    if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
    }
}

// Carregar dados dos jogadores
function loadPlayerData() {
    ensureDataDirectory();
    
    if (!fs.existsSync(DATA_FILE)) {
        return {};
    }
    
    try {
        const data = fs.readFileSync(DATA_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Erro ao carregar dados dos jogadores:', error);
        return {};
    }
}

// Salvar dados dos jogadores
function savePlayerData(data) {
    ensureDataDirectory();
    
    try {
        fs.writeFileSync(DATA_FILE, JSON.stringify(data, null, 2));
    } catch (error) {
        console.error('Erro ao salvar dados dos jogadores:', error);
    }
}

// Obter dados de um jogador específico
function getPlayerData(userId) {
    const allData = loadPlayerData();

    if (!allData[userId]) {
        allData[userId] = {
            superlockpoints: userId === '1182037836292161604' ? 70000 : 0, // Dinheiro especial para o usuário
            cards: [],
            lastPackTime: 0,
            boxes: [],
            vip: {
                type: 'NONE',
                purchaseDate: null,
                lastLuckySpinTime: 0,
                lastVipPackTime: 0,
                dailyLuckySpinsUsed: 0,
                lastDailyReset: 0
            }
        };
        savePlayerData(allData);
    }

    // Garantir que o VIP existe para usuários existentes
    if (!allData[userId].vip) {
        allData[userId].vip = {
            type: 'NONE',
            purchaseDate: null,
            lastLuckySpinTime: 0,
            lastVipPackTime: 0,
            dailyLuckySpinsUsed: 0,
            lastDailyReset: 0
        };
        savePlayerData(allData);
    }

    // Garantir que boxes existe para usuários existentes
    if (!allData[userId].boxes) {
        allData[userId].boxes = [];
        savePlayerData(allData);
    }
    
    return allData[userId];
}

// Atualizar dados de um jogador
function updatePlayerData(userId, newData) {
    const allData = loadPlayerData();
    allData[userId] = { ...allData[userId], ...newData };
    savePlayerData(allData);
    return allData[userId];
}

// Adicionar carta ao inventário do jogador
function addCardToPlayer(userId, cardName, rarity) {
    const playerData = getPlayerData(userId);
    
    const card = {
        name: cardName,
        rarity: rarity,
        obtainedAt: Date.now(),
        id: generateCardId()
    };
    
    playerData.cards.push(card);
    updatePlayerData(userId, playerData);
    
    return card;
}

// Remover carta do inventário do jogador
function removeCardFromPlayer(userId, cardId) {
    const playerData = getPlayerData(userId);
    const cardIndex = playerData.cards.findIndex(card => card.id === cardId);
    
    if (cardIndex === -1) {
        return null;
    }
    
    const removedCard = playerData.cards.splice(cardIndex, 1)[0];
    updatePlayerData(userId, playerData);
    
    return removedCard;
}

// Adicionar SuperLockPoints ao jogador (pode ser negativo para deduzir)
function addSuperlockPoints(userId, amount) {
    const playerData = getPlayerData(userId);
    playerData.superlockpoints += amount;

    // Garantir que não fique negativo
    if (playerData.superlockpoints < 0) {
        playerData.superlockpoints = 0;
    }

    updatePlayerData(userId, playerData);

    return playerData.superlockpoints;
}

// Verificar se jogador tem SuperLockPoints suficientes
function hasEnoughSuperlockPoints(userId, amount) {
    const playerData = getPlayerData(userId);
    return playerData.superlockpoints >= amount;
}

// Remover múltiplas cartas por IDs
function removeMultipleCards(userId, cardIds) {
    const playerData = getPlayerData(userId);
    const removedCards = [];

    cardIds.forEach(cardId => {
        const cardIndex = playerData.cards.findIndex(card => card.id === cardId);
        if (cardIndex !== -1) {
            const removedCard = playerData.cards.splice(cardIndex, 1)[0];
            removedCards.push(removedCard);
        }
    });

    updatePlayerData(userId, playerData);
    return removedCards;
}

// Obter cartas por raridade
function getCardsByRarity(userId, rarity) {
    const playerData = getPlayerData(userId);
    return playerData.cards.filter(card => card.rarity === rarity);
}

// Obter cartas por nome (busca parcial)
function getCardsByName(userId, cardName) {
    const playerData = getPlayerData(userId);
    return playerData.cards.filter(card =>
        card.name.toLowerCase().includes(cardName.toLowerCase())
    );
}

// Funções VIP
function setPlayerVip(userId, vipType) {
    const playerData = getPlayerData(userId);
    playerData.vip = {
        type: vipType,
        purchaseDate: Date.now(),
        lastLuckySpinTime: 0,
        lastVipPackTime: 0,
        dailyLuckySpinsUsed: 0,
        lastDailyReset: Date.now()
    };
    updatePlayerData(userId, playerData);
    return playerData.vip;
}

function getPlayerVip(userId) {
    const playerData = getPlayerData(userId);

    // Garantir que o VIP existe e tem todas as propriedades necessárias
    if (!playerData.vip) {
        return {
            type: 'NONE',
            purchaseDate: null,
            lastLuckySpinTime: 0,
            lastVipPackTime: 0,
            dailyLuckySpinsUsed: 0,
            lastDailyReset: 0
        };
    }

    return playerData.vip;
}

function canUseLuckySpin(userId) {
    const playerData = getPlayerData(userId);
    const vip = playerData.vip;

    if (vip.type !== 'GALAXIAL' && vip.type !== 'MEGAVIP') {
        return false;
    }

    const now = Date.now();
    const fourDays = 4 * 24 * 60 * 60 * 1000; // 4 dias em ms

    if (vip.type === 'GALAXIAL') {
        return (now - vip.lastLuckySpinTime) >= fourDays;
    }

    if (vip.type === 'MEGAVIP') {
        // Reset diário para MEGAVIP
        const today = new Date().toDateString();
        const lastReset = new Date(vip.lastDailyReset).toDateString();

        if (today !== lastReset) {
            vip.dailyLuckySpinsUsed = 0;
            vip.lastDailyReset = now;
            updatePlayerData(userId, playerData);
        }

        return vip.dailyLuckySpinsUsed < 3;
    }

    return false;
}

function useLuckySpin(userId) {
    const playerData = getPlayerData(userId);
    const vip = playerData.vip;

    if (vip.type === 'GALAXIAL') {
        vip.lastLuckySpinTime = Date.now();
    } else if (vip.type === 'MEGAVIP') {
        vip.dailyLuckySpinsUsed++;
    }

    updatePlayerData(userId, playerData);
}

function canUseVipPack(userId) {
    const playerData = getPlayerData(userId);
    const vip = playerData.vip;

    if (vip.type !== 'MEGAVIP') {
        return false;
    }

    const now = Date.now();
    const oneDay = 24 * 60 * 60 * 1000; // 1 dia em ms

    return (now - vip.lastVipPackTime) >= oneDay;
}

function useVipPack(userId) {
    const playerData = getPlayerData(userId);
    playerData.vip.lastVipPackTime = Date.now();
    updatePlayerData(userId, playerData);
}

// Funções para Box System
function addBoxToPlayer(userId) {
    const playerData = getPlayerData(userId);
    const boxId = Date.now().toString() + Math.random().toString(36).substr(2, 9);

    const box = {
        id: boxId,
        purchaseTime: Date.now(),
        opened: false,
        cards: generateBoxCards(), // 3 cartas aleatórias
        autoOpenTime: Date.now() + (2 * 60 * 1000) // 2 minutos para auto-abrir
    };

    playerData.boxes.push(box);
    updatePlayerData(userId, playerData);

    return boxId;
}

function generateBoxCards() {
    const cards = [];

    // Sistema de raridade para boxes (mais chances de cartas ruins)
    const boxRarities = [
        'COMUM', 'COMUM', 'COMUM', 'COMUM', 'COMUM', // 50% comum
        'INCOMUM', 'INCOMUM', 'INCOMUM', // 30% incomum
        'RARO', 'RARO', // 20% raro
        'LENDARIO' // 10% lendário (muito raro)
    ];

    for (let i = 0; i < 3; i++) {
        const rarity = boxRarities[Math.floor(Math.random() * boxRarities.length)];
        const { getRandomCard } = require('../config/cards');
        const cardName = getRandomCard(rarity);

        cards.push({
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            name: cardName,
            rarity: rarity
        });
    }

    return cards;
}

function getPlayerBoxes(userId) {
    const playerData = getPlayerData(userId);
    return playerData.boxes || [];
}

function openBox(userId, boxId) {
    const playerData = getPlayerData(userId);
    const boxIndex = playerData.boxes.findIndex(box => box.id === boxId);

    if (boxIndex === -1) {
        return null;
    }

    const box = playerData.boxes[boxIndex];
    if (box.opened) {
        return null;
    }

    // Marcar como aberta
    box.opened = true;
    box.openTime = Date.now();

    updatePlayerData(userId, playerData);
    return box;
}

function claimBoxCard(userId, boxId, cardIndex) {
    const playerData = getPlayerData(userId);
    const box = playerData.boxes.find(box => box.id === boxId);

    if (!box || !box.opened || cardIndex >= box.cards.length) {
        return null;
    }

    const card = box.cards[cardIndex];
    if (card.claimed) {
        return null;
    }

    // Adicionar carta ao inventário do jogador
    const addedCard = addCardToPlayer(userId, card.name, card.rarity);

    // Marcar carta como coletada na box
    card.claimed = true;
    card.claimTime = Date.now();
    card.inventoryId = addedCard.id; // Salvar referência da carta no inventário

    // Salvar dados atualizados
    updatePlayerData(userId, playerData);

    console.log(`Carta ${card.name} coletada da box ${boxId} e adicionada ao inventário com ID ${addedCard.id}`);

    return addedCard;
}

function removeBox(userId, boxId) {
    const playerData = getPlayerData(userId);
    const boxIndex = playerData.boxes.findIndex(box => box.id === boxId);

    if (boxIndex !== -1) {
        playerData.boxes.splice(boxIndex, 1);
        updatePlayerData(userId, playerData);
        return true;
    }

    return false;
}

function autoOpenExpiredBoxes(userId) {
    const playerData = getPlayerData(userId);
    const now = Date.now();
    let autoOpened = [];

    playerData.boxes.forEach(box => {
        if (!box.opened && now >= box.autoOpenTime) {
            // Auto-abrir e adicionar todas as cartas automaticamente
            box.opened = true;
            box.openTime = now;
            box.autoOpened = true;

            box.cards.forEach(card => {
                if (!card.claimed) {
                    addCardToPlayer(userId, card.name, card.rarity);
                    card.claimed = true;
                    card.claimTime = now;
                }
            });

            autoOpened.push(box);
        }
    });

    if (autoOpened.length > 0) {
        updatePlayerData(userId, playerData);
    }

    return autoOpened;
}

// Gerar ID único para carta
function generateCardId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Verificar se jogador pode usar pack (cooldown)
function canUsePackNormal(userId) {
    const playerData = getPlayerData(userId);
    const { getPackCooldown } = require('../config/vip');
    const now = Date.now();

    // Garantir que o VIP existe
    const vipType = (playerData.vip && playerData.vip.type) ? playerData.vip.type : 'NONE';
    const cooldownTime = getPackCooldown(vipType);

    return (now - playerData.lastPackTime) >= cooldownTime;
}

// Atualizar último uso do pack
function updateLastPackTime(userId) {
    const playerData = getPlayerData(userId);
    playerData.lastPackTime = Date.now();
    updatePlayerData(userId, playerData);
}

// Obter tempo restante do cooldown
function getCooldownTimeLeft(userId) {
    const playerData = getPlayerData(userId);
    const { getPackCooldown } = require('../config/vip');
    const now = Date.now();

    // Garantir que o VIP existe
    const vipType = (playerData.vip && playerData.vip.type) ? playerData.vip.type : 'NONE';
    const cooldownTime = getPackCooldown(vipType);
    const timeLeft = cooldownTime - (now - playerData.lastPackTime);

    return Math.max(0, timeLeft);
}

module.exports = {
    getPlayerData,
    updatePlayerData,
    addCardToPlayer,
    removeCardFromPlayer,
    addSuperlockPoints,
    hasEnoughSuperlockPoints,
    removeMultipleCards,
    getCardsByRarity,
    getCardsByName,
    canUsePackNormal,
    updateLastPackTime,
    getCooldownTimeLeft,
    setPlayerVip,
    getPlayerVip,
    canUseLuckySpin,
    useLuckySpin,
    canUseVipPack,
    useVipPack,
    addBoxToPlayer,
    getPlayerBoxes,
    openBox,
    claimBoxCard,
    removeBox,
    autoOpenExpiredBoxes
};
