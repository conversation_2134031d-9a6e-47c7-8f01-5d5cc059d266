const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { getPlayerData, addSuperlockPoints } = require('../utils/database');
const { COIN_EMOJI } = require('../config/cards');

// IDs de administradores autorizados
const ADMIN_IDS = ['1182037836292161604'];

module.exports = {
    data: new SlashCommandBuilder()
        .setName('admin')
        .setDescription('🔧 Comandos administrativos')
        .addSubcommand(subcommand =>
            subcommand
                .setName('money')
                .setDescription('Dar dinheiro para um usuário')
                .addUserOption(option =>
                    option.setName('usuario')
                        .setDescription('Usuário para receber o dinheiro')
                        .setRequired(true))
                .addIntegerOption(option =>
                    option.setName('quantidade')
                        .setDescription('Quantidade de moedas')
                        .setRequired(true)
                        .setMinValue(1)
                        .setMaxValue(1000000)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('reset')
                .setDescription('Resetar dados de um usuário')
                .addUserOption(option =>
                    option.setName('usuario')
                        .setDescription('Usuário para resetar')
                        .setRequired(true))),

    async execute(interaction) {
        // Verificar se é admin
        if (!ADMIN_IDS.includes(interaction.user.id)) {
            await interaction.reply({
                content: '❌ **Acesso Negado!**\n\nApenas administradores podem usar este comando.',
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        const subcommand = interaction.options.getSubcommand();

        if (subcommand === 'money') {
            await this.giveMoney(interaction);
        } else if (subcommand === 'reset') {
            await this.resetUser(interaction);
        }
    },

    async giveMoney(interaction) {
        const targetUser = interaction.options.getUser('usuario');
        const amount = interaction.options.getInteger('quantidade');
        
        const playerData = getPlayerData(targetUser.id);
        const oldBalance = playerData.superlockpoints;
        const newBalance = addSuperlockPoints(targetUser.id, amount);

        const moneyEmbed = new EmbedBuilder()
            .setTitle('💰 Dinheiro Adicionado!')
            .setDescription(`**Admin:** ${interaction.user.username}\n**Usuário:** ${targetUser.username}\n**Quantidade:** ${amount} ${COIN_EMOJI}`)
            .setColor(0x00FF00)
            .addFields(
                {
                    name: '💳 Saldo Anterior',
                    value: `${oldBalance} ${COIN_EMOJI}`,
                    inline: true
                },
                {
                    name: '💰 Saldo Atual',
                    value: `${newBalance} ${COIN_EMOJI}`,
                    inline: true
                },
                {
                    name: '📈 Diferença',
                    value: `+${amount} ${COIN_EMOJI}`,
                    inline: true
                }
            )
            .setThumbnail(targetUser.displayAvatarURL())
            .setFooter({ 
                text: `Comando executado por ${interaction.user.username}`, 
                iconURL: interaction.user.displayAvatarURL() 
            })
            .setTimestamp();

        // SEMPRE limpar imagens anteriores e garantir que não há anexos
        const replyOptions = {
            embeds: [moneyEmbed],
            files: [], // Limpar arquivos
            attachments: [] // Limpar anexos
        };

        await interaction.reply(replyOptions);
    },

    async resetUser(interaction) {
        const targetUser = interaction.options.getUser('usuario');
        
        // Resetar dados do usuário
        const fs = require('fs');
        const path = require('path');
        const dataPath = path.join(__dirname, '..', 'data', 'players.json');
        
        let allData = {};
        if (fs.existsSync(dataPath)) {
            allData = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
        }
        
        // Remover dados do usuário
        delete allData[targetUser.id];
        
        // Salvar dados
        fs.writeFileSync(dataPath, JSON.stringify(allData, null, 2));

        const resetEmbed = new EmbedBuilder()
            .setTitle('🔄 Usuário Resetado!')
            .setDescription(`**Admin:** ${interaction.user.username}\n**Usuário:** ${targetUser.username}\n\n✅ **Todos os dados foram resetados:**\n• Saldo zerado\n• Cartas removidas\n• VIP removido\n• Boxes removidas`)
            .setColor(0xFF6B6B)
            .setThumbnail(targetUser.displayAvatarURL())
            .setFooter({ 
                text: `Reset executado por ${interaction.user.username}`, 
                iconURL: interaction.user.displayAvatarURL() 
            })
            .setTimestamp();

        // SEMPRE limpar imagens anteriores e garantir que não há anexos
        const replyOptions = {
            embeds: [resetEmbed],
            files: [], // Limpar arquivos
            attachments: [] // Limpar anexos
        };

        await interaction.reply(replyOptions);
    }
};
