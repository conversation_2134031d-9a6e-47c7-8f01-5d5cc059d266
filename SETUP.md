# 🚀 Guia de Configuração - MeGa Football Community Bot

## 📋 Passo a Passo para Configurar o Bot

### 1. 🤖 Criar o Bot no Discord

1. Acesse o [Discord Developer Portal](https://discord.com/developers/applications)
2. Clique em "New Application"
3. Dê um nome ao seu bot (ex: "MeGa Football Bot")
4. Vá para a aba "Bot"
5. Clique em "Add Bot"
6. **IMPORTANTE**: Copie o Token do bot (você vai precisar dele)

### 2. 🔧 Configurar Permissões

Na aba "Bot":
- ✅ Marque "Send Messages"
- ✅ Marque "Use Slash Commands" 
- ✅ Marque "Embed Links"
- ✅ Marque "Attach Files"
- ✅ Marque "Read Message History"

### 3. 🔗 Gerar Link de Convite

1. Vá para a aba "OAuth2" > "URL Generator"
2. Em **Scopes**, marque:
   - ✅ `bot`
   - ✅ `applications.commands`
3. Em **Bot Permissions**, marque:
   - ✅ Send Messages
   - ✅ Use Slash Commands
   - ✅ Embed Links
   - ✅ Attach Files
   - ✅ Read Message History
4. Copie a URL gerada e use para convidar o bot ao seu servidor

### 4. 📝 Configurar Variáveis de Ambiente

1. Copie o arquivo `.env.example` para `.env`:
```bash
cp .env.example .env
```

2. Edite o arquivo `.env` com suas informações:
```env
# Token do bot (obtido no Discord Developer Portal)
DISCORD_TOKEN=MTIzNDU2Nzg5MDEyMzQ1Njc4OQ.GhIjKl.MnOpQrStUvWxYzAbCdEfGhIjKlMnOpQrStUvWx

# ID da aplicação (obtido no Discord Developer Portal)
CLIENT_ID=123456789012345678

# ID do servidor (opcional - clique com botão direito no servidor e "Copiar ID")
GUILD_ID=987654321098765432
```

### 5. 🎯 Como Obter os IDs Necessários

#### Token do Bot:
- Discord Developer Portal > Sua Aplicação > Bot > Token

#### Client ID (Application ID):
- Discord Developer Portal > Sua Aplicação > General Information > Application ID

#### Guild ID (Server ID):
- No Discord, clique com botão direito no nome do servidor > "Copiar ID"
- **Nota**: Você precisa ativar o "Modo Desenvolvedor" nas configurações do Discord

### 6. 🏃‍♂️ Executar o Bot

```bash
# Instalar dependências (se ainda não fez)
npm install

# Executar o bot
npm start

# Para desenvolvimento com auto-reload
npm run dev
```

### 7. ✅ Verificar se Funcionou

Se tudo estiver correto, você verá no console:
```
✅ Comando carregado: lockpack
✅ Comando carregado: sell  
✅ Comando carregado: inv
🔄 Registrando 3 comandos slash...
✅ 3 comandos slash registrados com sucesso!
🤖 Bot logado como MeGa Football Bot#1234!
🎴 MeGa Football Community Bot está online!
```

### 8. 🎮 Testar os Comandos

No seu servidor Discord, digite:
- `/lockpack` - Para abrir packs
- `/inv` - Para ver inventário
- `/sell` - Para vender cartas

## 🔧 Solução de Problemas

### ❌ "Invalid Token"
- Verifique se o token no `.env` está correto
- Certifique-se de que não há espaços extras

### ❌ "Missing Permissions"
- Verifique se o bot tem as permissões necessárias no servidor
- Recrie o link de convite com as permissões corretas

### ❌ "Commands not appearing"
- Aguarde alguns minutos (pode demorar para sincronizar)
- Tente sair e entrar no servidor novamente
- Execute `node deploy-commands.js` manualmente

### ❌ "Images not loading"
- Verifique se as imagens estão na pasta `images/`
- Certifique-se de que os nomes dos arquivos estão em minúsculo
- Todas as imagens devem ter extensão `.jpg`

## 📁 Estrutura de Arquivos Esperada

```
bot klayver/
├── images/                 # ✅ Já existe
│   ├── okuhito.jpg        # ✅ Já existe
│   ├── messi.jpg          # ✅ Já existe
│   └── ...                # ✅ Todas as outras imagens
├── commands/              # ✅ Criado
├── config/                # ✅ Criado
├── utils/                 # ✅ Criado
├── data/                  # 🔄 Será criado automaticamente
├── node_modules/          # ✅ Criado após npm install
├── .env                   # ❗ VOCÊ PRECISA CRIAR
├── index.js               # ✅ Criado
└── package.json           # ✅ Criado
```

## 🎉 Pronto!

Seu bot MeGa Football Community está configurado e pronto para uso! 

Os jogadores podem usar `/lockpack` para obter cartas, `/inv` para ver o inventário e `/sell` para vender cartas e ganhar SuperLockPoints! 🪙
