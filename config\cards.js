// Configuração das cartas e raridades
const COIN_EMOJI = '<:coin:1383505218175504585>';

const RARITIES = {
    COMUM: {
        name: 'Comum',
        color: 0x808080, // Cinza
        price: 5,
        chance: 45, // 45%
        emoji: '⚪'
    },
    INCOMUM: {
        name: 'Incomum',
        color: 0x00FF00, // Verde
        price: 10,
        chance: 30, // 30%
        emoji: '🟢'
    },
    RARO: {
        name: '<PERSON><PERSON>',
        color: 0x0080FF, // Azul
        price: 15,
        chance: 15, // 15%
        emoji: '🔵'
    },
    LENDARIO: {
        name: '<PERSON>d<PERSON><PERSON>',
        color: 0x8000FF, // Roxo
        price: 30,
        chance: 7, // 7%
        emoji: '🟣'
    },
    MISTICO: {
        name: '<PERSON><PERSON><PERSON><PERSON>',
        color: 0xFF0080, // Rosa/Magenta
        price: 50,
        chance: 2.5, // 2.5%
        emoji: '🔮'
    },
    WORLD_CLASS: {
        name: 'World Class',
        color: 0xFFD700, // Dourado
        price: 60,
        chance: 0.5, // 0.5%
        emoji: '👑'
    }
};

const CARDS = {
    COMUM: [
        'okuhito',
        'kyora',
        'tokimitsu',
        'kurona',
        'kuon',
        'ishikari',
        'nanase',
        'igaguri',
        'gagamaru'
    ],
    INCOMUM: [
        'palmer',
        'gyokeres',
        'mitoma',
        'messi',
        'zico',
        'C.ronaldo'
    ],
    RARO: [
        'isagi',
        'kunigami',
        'nel nagi',
        'chigiri',
        'nikko'
    ],
    LENDARIO: [
        'karasu',
        'bachira',
        'reo',
        'nagi',
        'Mbappé',
        'raichi',
        'gagamaru (88 over)'
    ],
    MISTICO: [
        'yukimia',
        'shidou',
        'barou',
        'rin itoshi'
    ],
    WORLD_CLASS: [
        'loki',
        'sae itoshi'
    ]
};

// Função para sortear raridade baseada nas chances
function getRandomRarity() {
    const random = Math.random() * 100;
    let cumulative = 0;

    for (const [key, rarity] of Object.entries(RARITIES)) {
        cumulative += rarity.chance;
        if (random <= cumulative) {
            return key;
        }
    }

    return 'COMUM'; // Fallback
}

// Função para sortear raridade do Lucky Pack (apenas 3 melhores raridades)
function getLuckyPackRarity() {
    const luckyRarities = {
        LENDARIO: 60,    // 60%
        MISTICO: 30,     // 30%
        WORLD_CLASS: 10  // 10%
    };

    const random = Math.random() * 100;
    let cumulative = 0;

    for (const [key, chance] of Object.entries(luckyRarities)) {
        cumulative += chance;
        if (random <= cumulative) {
            return key;
        }
    }

    return 'LENDARIO'; // Fallback
}

// Função para sortear carta de uma raridade específica
function getRandomCard(rarity) {
    const cards = CARDS[rarity];
    return cards[Math.floor(Math.random() * cards.length)];
}

// Função para gerar nome de attachment seguro para Discord
function getSafeAttachmentName(cardName) {
    // Substituir espaços e caracteres especiais por underscore
    return cardName.replace(/[^a-zA-Z0-9._-]/g, '_') + '.jpg';
}

// Função para obter informações de uma carta
function getCardInfo(cardName) {
    for (const [rarityKey, cards] of Object.entries(CARDS)) {
        if (cards.includes(cardName)) {
            return {
                name: cardName,
                rarity: rarityKey,
                rarityInfo: RARITIES[rarityKey],
                imagePath: `./images/${cardName}.jpg`, // Usar nome exato da carta
                safeAttachmentName: getSafeAttachmentName(cardName) // Nome seguro para Discord
            };
        }
    }
    return null;
}

module.exports = {
    RARITIES,
    CARDS,
    COIN_EMOJI,
    getRandomRarity,
    getLuckyPackRarity,
    getRandomCard,
    getCardInfo,
    getSafeAttachmentName
};
