require('dotenv').config();
const { REST, Routes } = require('discord.js');
const fs = require('fs');
const path = require('path');

const commands = [];

// Carregar comandos
const commandsPath = path.join(__dirname, 'commands');
const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));

for (const file of commandFiles) {
    const filePath = path.join(commandsPath, file);
    const command = require(filePath);
    
    if ('data' in command && 'execute' in command) {
        commands.push(command.data.toJSON());
        console.log(`✅ Comando carregado: ${command.data.name}`);
    } else {
        console.log(`⚠️ Comando em ${filePath} está faltando propriedades "data" ou "execute".`);
    }
}

// Construir e preparar uma instância do módulo REST
const rest = new REST().setToken(process.env.DISCORD_TOKEN);

// Deploy dos comandos
(async () => {
    try {
        console.log(`🔄 Iniciando refresh de ${commands.length} comandos de aplicação (/).`);

        // O método put é usado para atualizar completamente todos os comandos na guild com o conjunto atual
        const data = await rest.put(
            Routes.applicationCommands(process.env.CLIENT_ID),
            { body: commands },
        );

        console.log(`✅ Recarregados com sucesso ${data.length} comandos de aplicação (/).`);
    } catch (error) {
        // E claro, certifique-se de capturar e registrar quaisquer erros!
        console.error('❌ Erro ao fazer deploy dos comandos:', error);
    }
})();
