const { <PERSON><PERSON><PERSON><PERSON>mandB<PERSON>er, <PERSON>bedB<PERSON>er, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, ModalBuilder, TextInputBuilder, TextInputStyle, AttachmentBuilder } = require('discord.js');
const { getPlayerData, removeCardFromPlayer, addSuperlockPoints, getPlayerVip } = require('../utils/database');
const { RARITIES, COIN_EMOJI } = require('../config/cards');
const { applySellMultiplier } = require('../config/vip');
const fs = require('fs');
const path = require('path');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('sell')
        .setDescription('💰 Menu de vendas - Venda suas cartas facilmente!'),

    async execute(interaction) {
        await this.showSellMenu(interaction);
    },

    async showSellMenu(interaction) {
        const userId = interaction.user.id;
        const playerData = getPlayerData(userId);

        if (playerData.cards.length === 0) {
            await interaction.reply({
                content: '📭 Você não tem cartas para vender! Use `/lockpack` para obter cartas.',
                ephemeral: true
            });
            return;
        }

        // Contar cartas por raridade
        const rarityCount = {};
        Object.keys(RARITIES).forEach(rarity => {
            rarityCount[rarity] = playerData.cards.filter(card => card.rarity === rarity).length;
        });

        const menuEmbed = new EmbedBuilder()
            .setTitle('💰 Menu de Vendas - MeGa Football Community')
            .setDescription(`Escolha como você quer vender suas cartas:\n\n💰 **Seu Saldo:** ${playerData.superlockpoints} ${COIN_EMOJI}\n🎴 **Total de Cartas:** ${playerData.cards.length}`)
            .setColor(0x00AE86);

        // Adicionar contagem por raridade
        Object.entries(rarityCount).forEach(([rarity, count]) => {
            if (count > 0) {
                const rarityInfo = RARITIES[rarity];
                menuEmbed.addFields({
                    name: `${rarityInfo.emoji} ${rarityInfo.name}`,
                    value: `${count} cartas • ${rarityInfo.price} ${COIN_EMOJI} cada`,
                    inline: true
                });
            }
        });

        // Criar select menu para vender por raridade
        const rarityOptions = Object.entries(rarityCount)
            .filter(([rarity, count]) => count > 0)
            .map(([rarity, count]) => {
                const rarityInfo = RARITIES[rarity];
                const totalValue = count * rarityInfo.price;
                return {
                    label: `${rarityInfo.name} (${count} cartas)`,
                    description: `Vender todas por ${totalValue} ${COIN_EMOJI}`,
                    value: `sell_rarity_${rarity}`,
                    emoji: rarityInfo.emoji
                };
            });

        const components = [];

        if (rarityOptions.length > 0) {
            const raritySelect = new ActionRowBuilder()
                .addComponents(
                    new StringSelectMenuBuilder()
                        .setCustomId('sell_by_rarity')
                        .setPlaceholder('🎯 Vender todas as cartas de uma raridade')
                        .addOptions(rarityOptions)
                );
            components.push(raritySelect);
        }

        // Botões para outras opções
        const buttonRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('sell_by_name')
                    .setLabel('Vender por Nome')
                    .setStyle(ButtonStyle.Secondary)
            );

        components.push(buttonRow);

        await interaction.reply({ embeds: [menuEmbed], components: components });
    },

    async handleSellInteraction(interaction) {
        // Verificar se é o usuário que executou o comando original
        const originalUserId = interaction.message.interaction?.user?.id;
        if (originalUserId && interaction.user.id !== originalUserId) {
            await interaction.reply({
                content: '❌ Apenas quem executou o comando pode interagir com ele!',
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        if (interaction.isStringSelectMenu() && interaction.customId === 'sell_by_rarity') {
            const rarity = interaction.values[0].split('_')[2];
            await this.sellByRarity(interaction, rarity);
        } else if (interaction.isButton() && interaction.customId === 'sell_by_name') {
            await this.showNameSellModal(interaction);
        }
    },

    async sellByRarity(interaction, rarity) {
        const userId = interaction.user.id;
        const playerData = getPlayerData(userId);
        const cardsToSell = playerData.cards.filter(card => card.rarity === rarity);
        
        if (cardsToSell.length === 0) {
            await interaction.reply({
                content: '❌ Você não tem cartas desta raridade!',
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        const rarityInfo = RARITIES[rarity];
        const baseValue = cardsToSell.length * rarityInfo.price;

        // Aplicar multiplicador VIP se for Business VIP
        const vipData = getPlayerVip(userId);
        const totalValue = applySellMultiplier(baseValue, vipData.type);

        // Vender todas as cartas da raridade
        cardsToSell.forEach(card => {
            removeCardFromPlayer(userId, card.id);
        });

        // Adicionar SuperLockPoints
        const newBalance = addSuperlockPoints(userId, totalValue);
        
        const successEmbed = new EmbedBuilder()
            .setTitle('💰 Venda Realizada!')
            .setDescription(`Vendidas **${cardsToSell.length}** cartas ${rarityInfo.emoji} ${rarityInfo.name}`)
            .setColor(0x00FF00)
            .addFields(
                {
                    name: '💰 Valor Recebido',
                    value: `${totalValue} ${COIN_EMOJI}`,
                    inline: true
                },
                {
                    name: `${COIN_EMOJI} Novo Saldo`,
                    value: `${newBalance} SuperLockPoints`,
                    inline: true
                }
            )
            .setFooter({ 
                text: `Vendido por ${interaction.user.username}`, 
                iconURL: interaction.user.displayAvatarURL() 
            })
            .setTimestamp();

        await interaction.update({ embeds: [successEmbed], components: [] });
    },

    async showNameSellModal(interaction) {
        const modal = new ModalBuilder()
            .setCustomId('sell_by_name_modal')
            .setTitle('Vender Carta por Nome');

        const nameInput = new TextInputBuilder()
            .setCustomId('card_name')
            .setLabel('Nome da carta (ou parte do nome)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Ex: messi, nagi, bachira...')
            .setRequired(true)
            .setMaxLength(50);

        const quantityInput = new TextInputBuilder()
            .setCustomId('card_quantity')
            .setLabel('Quantidade (deixe vazio para vender todas)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Ex: 1, 2, 3... (vazio = todas)')
            .setRequired(false)
            .setMaxLength(3);

        const nameRow = new ActionRowBuilder().addComponents(nameInput);
        const quantityRow = new ActionRowBuilder().addComponents(quantityInput);

        modal.addComponents(nameRow, quantityRow);

        await interaction.showModal(modal);
    },

    async handleNameSellModal(interaction) {
        const cardName = interaction.fields.getTextInputValue('card_name').toLowerCase().trim();
        const quantityInput = interaction.fields.getTextInputValue('card_quantity').trim();
        const quantity = quantityInput ? parseInt(quantityInput) : null;
        
        const userId = interaction.user.id;
        const playerData = getPlayerData(userId);
        
        // Buscar cartas que contenham o nome
        const matchingCards = playerData.cards.filter(card => 
            card.name.toLowerCase().includes(cardName)
        );
        
        if (matchingCards.length === 0) {
            await interaction.reply({
                content: `❌ Nenhuma carta encontrada com o nome "${cardName}"!`,
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        // Determinar quantas cartas vender
        let cardsToSell;
        if (quantity && quantity > 0) {
            if (quantity > matchingCards.length) {
                await interaction.reply({
                    content: `❌ Você só tem ${matchingCards.length} carta(s) com "${cardName}", mas quer vender ${quantity}!`,
                    flags: 64 // MessageFlags.Ephemeral
                });
                return;
            }
            cardsToSell = matchingCards.slice(0, quantity);
        } else {
            cardsToSell = matchingCards;
        }

        // Calcular valor total
        const baseValue = cardsToSell.reduce((sum, card) => sum + RARITIES[card.rarity].price, 0);

        // Aplicar multiplicador VIP se for Business VIP
        const vipData = getPlayerVip(userId);
        const totalValue = applySellMultiplier(baseValue, vipData.type);

        // Vender as cartas
        cardsToSell.forEach(card => {
            removeCardFromPlayer(userId, card.id);
        });

        // Adicionar SuperLockPoints
        const newBalance = addSuperlockPoints(userId, totalValue);
        
        // Criar embed de resultado
        const successEmbed = new EmbedBuilder()
            .setTitle('💰 Venda por Nome Realizada!')
            .setDescription(`Vendidas **${cardsToSell.length}** carta(s) com "${cardName}"`)
            .setColor(0x00FF00)
            .addFields(
                {
                    name: '💰 Valor Recebido',
                    value: `${totalValue} ${COIN_EMOJI}`,
                    inline: true
                },
                {
                    name: `${COIN_EMOJI} Novo Saldo`,
                    value: `${newBalance} SuperLockPoints`,
                    inline: true
                },
                {
                    name: '🎴 Cartas Vendidas',
                    value: cardsToSell.slice(0, 10).map(card => {
                        const rarityInfo = RARITIES[card.rarity];
                        return `• ${card.name} ${rarityInfo.emoji} (${rarityInfo.price} ${COIN_EMOJI})`;
                    }).join('\n') + (cardsToSell.length > 10 ? `\n*... e mais ${cardsToSell.length - 10} cartas*` : ''),
                    inline: false
                }
            )
            .setFooter({ 
                text: `Vendido por ${interaction.user.username}`, 
                iconURL: interaction.user.displayAvatarURL() 
            })
            .setTimestamp();

        await interaction.reply({ embeds: [successEmbed] });
    },

    async handleQuickSell(interaction) {
        const cardId = interaction.customId.split('_')[1];
        const userId = interaction.user.id;

        // Verificar se é o usuário que executou o comando original
        const originalUserId = interaction.message.interaction?.user?.id;
        if (originalUserId && userId !== originalUserId) {
            await interaction.reply({
                content: '❌ Apenas quem executou o comando pode interagir com ele!',
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }
        
        // Obter dados do jogador
        const playerData = getPlayerData(userId);
        
        // Encontrar a carta
        const card = playerData.cards.find(c => c.id === cardId);
        
        if (!card) {
            await interaction.reply({
                content: '❌ Carta não encontrada ou já foi vendida!',
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }
        
        // Obter informações da raridade
        const rarityInfo = RARITIES[card.rarity];

        // Aplicar multiplicador VIP se for Business VIP
        const vipData = getPlayerVip(userId);
        const sellValue = applySellMultiplier(rarityInfo.price, vipData.type);

        // Remover carta do inventário
        const removedCard = removeCardFromPlayer(userId, cardId);

        if (!removedCard) {
            await interaction.reply({
                content: '❌ Erro ao vender a carta. Tente novamente.',
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        // Adicionar SuperLockPoints
        const newBalance = addSuperlockPoints(userId, sellValue);
        
        // Mostrar valor com multiplicador se aplicável
        const displayValue = sellValue > rarityInfo.price ?
            `${sellValue} ${COIN_EMOJI} (${rarityInfo.price} x2 VIP)` :
            `${sellValue} ${COIN_EMOJI}`;

        // Criar embed de venda rápida
        const quickSellEmbed = new EmbedBuilder()
            .setTitle('⚡ QuickSell - MeGa Football Community')
            .setDescription(`**Jogador:** ${card.name}\n**Raridade:** ${rarityInfo.emoji} ${rarityInfo.name}\n**Preço:** ${displayValue}`)
            .setColor(0xFFD700) // Dourado para quicksell
            .addFields(
                {
                    name: '💳 Vendedor',
                    value: `${interaction.user.username}`,
                    inline: true
                },
                {
                    name: `${COIN_EMOJI} SuperLockPoints`,
                    value: `+${sellValue} (Total: ${newBalance})`,
                    inline: true
                }
            )
            .setFooter({
                text: `QuickSell realizada • ID: ${cardId}`,
                iconURL: interaction.user.displayAvatarURL()
            })
            .setTimestamp();

        // Tentar adicionar imagem da carta
        const imagePath = path.join(__dirname, '..', 'images', `${card.name}.jpg`);

        let attachment = null;
        let hasImage = false;

        if (fs.existsSync(imagePath)) {
            try {
                attachment = new AttachmentBuilder(imagePath, { name: `${card.name}.jpg` });
                quickSellEmbed.setImage(`attachment://${card.name}.jpg`);
                hasImage = true;
            } catch (error) {
                console.log(`Erro ao carregar imagem para ${card.name}:`, error.message);
                quickSellEmbed.setThumbnail(interaction.user.displayAvatarURL());
            }
        } else {
            quickSellEmbed.setThumbnail(interaction.user.displayAvatarURL());
        }

        const replyOptions = {
            embeds: [quickSellEmbed],
            components: [], // Remove os botões
            files: [], // Sempre limpar arquivos anteriores
            attachments: [] // Limpar anexos
        };

        // Adicionar nova imagem apenas se existir
        if (attachment && hasImage) {
            replyOptions.files = [attachment];
        }

        await interaction.update(replyOptions);
    }
};
