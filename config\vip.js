const { COIN_EMOJI } = require('./cards');

const VIP_TYPES = {
    NONE: {
        name: 'Sem VIP',
        price: 0,
        color: 0x808080,
        emoji: '⚪',
        benefits: []
    },
    BASE: {
        name: 'Base VIP',
        price: 5000,
        color: 0x00FF00,
        emoji: '🟢',
        benefits: [
            'Cooldown do pack reduzido para 17 minutos'
        ],
        cooldownReduction: 3 * 60 * 1000 // 3 minutos em ms
    },
    RARE: {
        name: 'Rare VIP',
        price: 10000,
        color: 0x0080FF,
        emoji: '🔵',
        benefits: [
            'Cooldown do pack reduzido para 15 minutos',
            'Desconto de 5% em todos os packs da loja'
        ],
        cooldownReduction: 5 * 60 * 1000, // 5 minutos em ms
        packDiscount: 0.05 // 5%
    },
    SOLAR: {
        name: 'Solar VIP',
        price: 20000,
        color: 0xFFAA00,
        emoji: '🟡',
        benefits: [
            'Cooldown do pack reduzido para 12 minutos',
            'Desconto de 10% em jogadores da loja',
            'Desconto de 15% em packs da loja'
        ],
        cooldownReduction: 8 * 60 * 1000, // 8 minutos em ms
        playerDiscount: 0.10, // 10%
        packDiscount: 0.15 // 15%
    },
    GALAXIAL: {
        name: 'Galaxial VIP',
        price: 40000,
        color: 0x8000FF,
        emoji: '🟣',
        benefits: [
            '1 Lucky Spin a cada 4 dias',
            'Cooldown do pack reduzido para 14 minutos',
            'Desconto de 30% em jogadores da loja'
        ],
        cooldownReduction: 6 * 60 * 1000, // 6 minutos em ms
        playerDiscount: 0.30, // 30%
        hasLuckySpin: true
    },
    MEGAVIP: {
        name: 'MegaVIP',
        price: 70000,
        color: 0xFF0080,
        emoji: '🔮',
        benefits: [
            'Desconto de 70% em packs e jogadores',
            '1 pack VIP gratuito por dia',
            '3 Lucky Spins por dia',
            'Cooldown do pack reduzido para 10 minutos'
        ],
        cooldownReduction: 10 * 60 * 1000, // 10 minutos em ms
        playerDiscount: 0.70, // 70%
        packDiscount: 0.70, // 70%
        hasLuckySpin: true,
        hasVipPack: true
    },
    EMPEROR: {
        name: 'Emperor VIP',
        price: 65000,
        color: 0x800080,
        emoji: '👑',
        benefits: [
            'Cooldown do pack reduzido para 8 minutos',
            'Desconto de 50% em packs e jogadores',
            '2 Lucky Spins por dia'
        ],
        cooldownReduction: 12 * 60 * 1000, // 12 minutos em ms
        playerDiscount: 0.50, // 50%
        packDiscount: 0.50, // 50%
        hasLuckySpin: true
    },
    BUSINESS: {
        name: 'Business VIP',
        price: 80000,
        color: 0xFFD700,
        emoji: '💼',
        benefits: [
            'Ganhe o DOBRO ao vender jogadores',
            'Cooldown do pack reduzido para 5 minutos',
            'Desconto de 25% em packs da loja'
        ],
        cooldownReduction: 15 * 60 * 1000, // 15 minutos em ms
        sellMultiplier: 2.0, // Dobro do dinheiro
        packDiscount: 0.25 // 25%
    }
};

// Função para obter cooldown do pack baseado no VIP
function getPackCooldown(vipType) {
    const baseCooldown = 20 * 60 * 1000; // 20 minutos base
    const vip = VIP_TYPES[vipType] || VIP_TYPES.NONE;
    return baseCooldown - (vip.cooldownReduction || 0);
}

// Função para calcular preço com desconto VIP
function applyVipDiscount(price, vipType, discountType) {
    const vip = VIP_TYPES[vipType] || VIP_TYPES.NONE;
    
    if (discountType === 'player' && vip.playerDiscount) {
        return Math.ceil(price * (1 - vip.playerDiscount));
    }
    
    if (discountType === 'pack' && vip.packDiscount) {
        return Math.ceil(price * (1 - vip.packDiscount));
    }
    
    return price;
}

// Função para calcular ganho de venda com multiplicador VIP
function applySellMultiplier(amount, vipType) {
    const vip = VIP_TYPES[vipType] || VIP_TYPES.NONE;
    return Math.floor(amount * (vip.sellMultiplier || 1.0));
}

// Função para verificar se VIP tem benefício específico
function hasVipBenefit(vipType, benefit) {
    const vip = VIP_TYPES[vipType] || VIP_TYPES.NONE;
    
    switch (benefit) {
        case 'luckySpin':
            return vip.hasLuckySpin || false;
        case 'vipPack':
            return vip.hasVipPack || false;
        default:
            return false;
    }
}

// Função para obter VIPs ordenados por preço
function getVipsSortedByPrice() {
    return Object.entries(VIP_TYPES)
        .filter(([key]) => key !== 'NONE')
        .sort(([, a], [, b]) => a.price - b.price);
}

module.exports = {
    VIP_TYPES,
    getPackCooldown,
    applyVipDiscount,
    applySellMultiplier,
    hasVipBenefit,
    getVipsSortedByPrice
};
