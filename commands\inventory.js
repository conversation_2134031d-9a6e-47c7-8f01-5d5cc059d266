const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { getPlayerData } = require('../utils/database');
const { RARITIES, COIN_EMOJI } = require('../config/cards');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('inv')
        .setDescription('Veja seu inventário de cartas')
        .addUserOption(option =>
            option.setName('jogador')
                .setDescription('Ver inventário de outro jogador (opcional)')
                .setRequired(false)),
    
    async execute(interaction) {
        const targetUser = interaction.options.getUser('jogador') || interaction.user;
        const userId = targetUser.id;
        
        // Obter dados do jogador
        const playerData = getPlayerData(userId);
        
        // Criar embed do inventário
        const inventoryEmbed = new EmbedBuilder()
            .setTitle(`🎒 Inventário - ${targetUser.username}`)
            .setColor(0x00AE86)
            .setThumbnail(targetUser.displayAvatarURL())
            .addFields(
                {
                    name: `${COIN_EMOJI} SuperLockPoints`,
                    value: `${playerData.superlockpoints}`,
                    inline: true
                },
                {
                    name: '🎴 Total de Cartas',
                    value: `${playerData.cards.length}`,
                    inline: true
                }
            )
            .setFooter({ 
                text: 'MeGa Football Community', 
                iconURL: interaction.client.user.displayAvatarURL() 
            })
            .setTimestamp();

        if (playerData.cards.length === 0) {
            inventoryEmbed.setDescription('📭 Inventário vazio! Use `/lockpack` para obter suas primeiras cartas.');
            await interaction.reply({ embeds: [inventoryEmbed] });
            return;
        }

        // Organizar cartas por raridade
        const cardsByRarity = {};
        
        // Inicializar contadores
        Object.keys(RARITIES).forEach(rarity => {
            cardsByRarity[rarity] = [];
        });
        
        // Agrupar cartas por raridade
        playerData.cards.forEach(card => {
            if (cardsByRarity[card.rarity]) {
                cardsByRarity[card.rarity].push(card);
            }
        });

        // Adicionar campos para cada raridade (apenas se houver cartas)
        Object.entries(cardsByRarity).forEach(([rarity, cards]) => {
            if (cards.length > 0) {
                const rarityInfo = RARITIES[rarity];
                
                // Limitar a 10 cartas por raridade para não exceder o limite do embed
                const displayCards = cards.slice(0, 10);
                const cardList = displayCards.map(card => 
                    `• **${card.name}** \`(ID: ${card.id})\``
                ).join('\n');
                
                const fieldValue = cards.length > 10 
                    ? `${cardList}\n*... e mais ${cards.length - 10} carta(s)*`
                    : cardList;

                inventoryEmbed.addFields({
                    name: `${rarityInfo.emoji} ${rarityInfo.name} (${cards.length})`,
                    value: fieldValue,
                    inline: false
                });
            }
        });

        // Adicionar informação sobre como vender cartas
        if (playerData.cards.length > 0) {
            inventoryEmbed.setDescription(
                '💡 **Como vender:** Use `/sell menu` para opções avançadas de venda!\n' +
                '🔍 **Opções:** Vender por raridade, quantidade específica ou ID individual.\n' +
                '🏪 **Loja:** Use `/shop` para comprar jogadores especiais!'
            );
        }

        await interaction.reply({ embeds: [inventoryEmbed] });
    }
};
