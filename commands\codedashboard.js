const { <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');
const { getAllPromoCodes, createPromoCode, removePromoCode, editPromoCode, cleanExpiredCodes } = require('../utils/database');
const { COIN_EMOJI } = require('../config/cards');

// Senha de acesso ao dashboard
const DASHBOARD_PASSWORD = '015010';

module.exports = {
    data: new SlashCommandBuilder()
        .setName('codedashboard')
        .setDescription('🔧 Dashboard administrativo para gerenciar códigos promocionais')
        .addStringOption(option =>
            option.setName('senha')
                .setDescription('Senha de acesso ao dashboard')
                .setRequired(true)),

    async execute(interaction) {
        const senha = interaction.options.getString('senha');
        
        // Verificar senha
        if (senha !== DASHBOARD_PASSWORD) {
            await interaction.reply({
                content: '❌ **Senha incorreta!**\n\nApenas administradores autorizados podem acessar o dashboard.',
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        await this.showDashboard(interaction);
    },

    async showDashboard(interaction) {
        // Limpar códigos expirados primeiro
        const removedCount = cleanExpiredCodes();
        
        const codes = getAllPromoCodes();
        const codeCount = Object.keys(codes).length;
        const activeCodes = Object.values(codes).filter(code => code.active).length;
        
        let statusText = '';
        if (removedCount > 0) {
            statusText = `\n🗑️ ${removedCount} código(s) expirado(s) removido(s)`;
        }

        const dashboardEmbed = new EmbedBuilder()
            .setTitle('🔧 Dashboard de Códigos Promocionais')
            .setDescription(`Gerencie códigos promocionais do MeGa Football Community${statusText}\n\n📊 **Estatísticas:**\n📝 Total de Códigos: ${codeCount}\n✅ Códigos Ativos: ${activeCodes}\n❌ Códigos Inativos: ${codeCount - activeCodes}`)
            .setColor(0x9B59B6)
            .setFooter({
                text: `Dashboard acessado por ${interaction.user.username}`,
                iconURL: interaction.user.displayAvatarURL()
            })
            .setTimestamp();

        // Mostrar alguns códigos ativos
        if (codeCount > 0) {
            const activeCodesList = Object.values(codes)
                .filter(code => code.active)
                .slice(0, 5)
                .map(code => {
                    const expiry = code.expirationTime ? 
                        `Expira: <t:${Math.floor(code.expirationTime / 1000)}:R>` : 
                        'Sem expiração';
                    return `• **${code.name}** - Usado ${code.usedCount}x\n  ${expiry}`;
                })
                .join('\n');

            if (activeCodesList) {
                dashboardEmbed.addFields({
                    name: '📋 Códigos Ativos Recentes',
                    value: activeCodesList + (activeCodes > 5 ? `\n*...e mais ${activeCodes - 5} códigos*` : ''),
                    inline: false
                });
            }
        }

        const buttonRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('dashboard_create')
                    .setLabel('Criar Código')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('dashboard_list')
                    .setLabel('Listar Códigos')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('dashboard_manage')
                    .setLabel('Gerenciar')
                    .setStyle(ButtonStyle.Secondary)
            );

        const replyOptions = {
            embeds: [dashboardEmbed],
            components: [buttonRow],
            files: [],
            attachments: []
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.editReply(replyOptions);
        } else {
            await interaction.reply(replyOptions);
        }
    },

    async handleDashboardButtons(interaction) {
        const customId = interaction.customId;

        if (customId === 'dashboard_create') {
            await this.showCreateModal(interaction);
        } else if (customId === 'dashboard_list') {
            await this.showCodesList(interaction);
        } else if (customId === 'dashboard_manage') {
            await this.showManageModal(interaction);
        } else if (customId.startsWith('dashboard_remove_')) {
            const codeName = customId.split('_')[2];
            await this.removeCode(interaction, codeName);
        } else if (customId === 'dashboard_back') {
            await this.showDashboard(interaction);
        }
    },

    async showCreateModal(interaction) {
        const modal = new ModalBuilder()
            .setCustomId('dashboard_create_modal')
            .setTitle('Criar Novo Código Promocional');

        const nameInput = new TextInputBuilder()
            .setCustomId('code_name')
            .setLabel('Nome do Código')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Ex: 900kmembers')
            .setRequired(true)
            .setMaxLength(50);

        const superlockpointsInput = new TextInputBuilder()
            .setCustomId('superlockpoints')
            .setLabel('SuperLockPoints (0 para nenhum)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Ex: 1000')
            .setRequired(true)
            .setMaxLength(10);

        const luckySpinsInput = new TextInputBuilder()
            .setCustomId('lucky_spins')
            .setLabel('Lucky Spins (0 para nenhum)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Ex: 3')
            .setRequired(true)
            .setMaxLength(5);

        const mysteryBoxesInput = new TextInputBuilder()
            .setCustomId('mystery_boxes')
            .setLabel('Mystery Boxes (0 para nenhum)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Ex: 2')
            .setRequired(true)
            .setMaxLength(5);

        const validityInput = new TextInputBuilder()
            .setCustomId('validity_days')
            .setLabel('Validade em dias (0 para sem expiração)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Ex: 7')
            .setRequired(true)
            .setMaxLength(5);

        const nameRow = new ActionRowBuilder().addComponents(nameInput);
        const superlockpointsRow = new ActionRowBuilder().addComponents(superlockpointsInput);
        const luckySpinsRow = new ActionRowBuilder().addComponents(luckySpinsInput);
        const mysteryBoxesRow = new ActionRowBuilder().addComponents(mysteryBoxesInput);
        const validityRow = new ActionRowBuilder().addComponents(validityInput);

        modal.addComponents(nameRow, superlockpointsRow, luckySpinsRow, mysteryBoxesRow, validityRow);

        await interaction.showModal(modal);
    },

    async handleCreateModal(interaction) {
        const codeName = interaction.fields.getTextInputValue('code_name').trim();
        const superlockpoints = parseInt(interaction.fields.getTextInputValue('superlockpoints')) || 0;
        const luckySpins = parseInt(interaction.fields.getTextInputValue('lucky_spins')) || 0;
        const mysteryBoxes = parseInt(interaction.fields.getTextInputValue('mystery_boxes')) || 0;
        const validityDays = parseInt(interaction.fields.getTextInputValue('validity_days')) || 0;

        if (!codeName) {
            await interaction.reply({
                content: '❌ Nome do código é obrigatório!',
                flags: 64
            });
            return;
        }

        if (superlockpoints < 0 || luckySpins < 0 || mysteryBoxes < 0 || validityDays < 0) {
            await interaction.reply({
                content: '❌ Valores não podem ser negativos!',
                flags: 64
            });
            return;
        }

        if (superlockpoints === 0 && luckySpins === 0 && mysteryBoxes === 0) {
            await interaction.reply({
                content: '❌ O código deve ter pelo menos uma recompensa!',
                flags: 64
            });
            return;
        }

        const rewards = {
            superlockpoints: superlockpoints,
            luckySpins: luckySpins,
            mysteryBoxes: mysteryBoxes
        };

        const result = createPromoCode(codeName, rewards, validityDays, interaction.user.id);

        if (result.success) {
            const successEmbed = new EmbedBuilder()
                .setTitle('✅ Código Criado com Sucesso!')
                .setDescription(`**Código:** ${codeName}`)
                .setColor(0x00FF00)
                .addFields(
                    {
                        name: '🎁 Recompensas',
                        value: [
                            superlockpoints > 0 ? `💰 ${superlockpoints} ${COIN_EMOJI}` : null,
                            luckySpins > 0 ? `🍀 ${luckySpins} Lucky Spins` : null,
                            mysteryBoxes > 0 ? `📦 ${mysteryBoxes} Mystery Boxes` : null
                        ].filter(Boolean).join('\n'),
                        inline: true
                    },
                    {
                        name: '⏰ Validade',
                        value: validityDays > 0 ? `${validityDays} dias` : 'Sem expiração',
                        inline: true
                    }
                )
                .setFooter({
                    text: `Criado por ${interaction.user.username}`,
                    iconURL: interaction.user.displayAvatarURL()
                })
                .setTimestamp();

            const backButton = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dashboard_back')
                        .setLabel('Voltar ao Dashboard')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.reply({
                embeds: [successEmbed],
                components: [backButton]
            });
        } else {
            await interaction.reply({
                content: `❌ ${result.message}`,
                flags: 64
            });
        }
    },

    async showCodesList(interaction) {
        const codes = getAllPromoCodes();
        const codeEntries = Object.values(codes);

        if (codeEntries.length === 0) {
            const noCodesEmbed = new EmbedBuilder()
                .setTitle('📋 Lista de Códigos')
                .setDescription('❌ Nenhum código promocional encontrado.')
                .setColor(0x95A5A6);

            const backButton = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dashboard_back')
                        .setLabel('Voltar ao Dashboard')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({
                embeds: [noCodesEmbed],
                components: [backButton]
            });
            return;
        }

        const listEmbed = new EmbedBuilder()
            .setTitle('📋 Lista de Códigos Promocionais')
            .setDescription(`Total: ${codeEntries.length} código(s)`)
            .setColor(0x3498DB);

        // Mostrar códigos em grupos de 10
        const activeList = codeEntries
            .filter(code => code.active)
            .slice(0, 10)
            .map(code => {
                const rewards = [];
                if (code.rewards.superlockpoints > 0) rewards.push(`${code.rewards.superlockpoints} ${COIN_EMOJI}`);
                if (code.rewards.luckySpins > 0) rewards.push(`${code.rewards.luckySpins} 🍀`);
                if (code.rewards.mysteryBoxes > 0) rewards.push(`${code.rewards.mysteryBoxes} 📦`);

                const expiry = code.expirationTime ?
                    `Expira: <t:${Math.floor(code.expirationTime / 1000)}:R>` :
                    'Sem expiração';

                return `**${code.name}**\n• Recompensas: ${rewards.join(', ')}\n• Usado: ${code.usedCount}x\n• ${expiry}`;
            })
            .join('\n\n');

        if (activeList) {
            listEmbed.addFields({
                name: '✅ Códigos Ativos',
                value: activeList,
                inline: false
            });
        }

        const backButton = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('dashboard_back')
                    .setLabel('Voltar ao Dashboard')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({
            embeds: [listEmbed],
            components: [backButton]
        });
    },

    async showManageModal(interaction) {
        const modal = new ModalBuilder()
            .setCustomId('dashboard_manage_modal')
            .setTitle('Gerenciar Código Promocional');

        const codeNameInput = new TextInputBuilder()
            .setCustomId('manage_code_name')
            .setLabel('Nome do Código para Gerenciar')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Ex: 900kmembers')
            .setRequired(true)
            .setMaxLength(50);

        const actionInput = new TextInputBuilder()
            .setCustomId('manage_action')
            .setLabel('Ação (remover, editar)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('remover ou editar')
            .setRequired(true)
            .setMaxLength(10);

        const nameRow = new ActionRowBuilder().addComponents(codeNameInput);
        const actionRow = new ActionRowBuilder().addComponents(actionInput);

        modal.addComponents(nameRow, actionRow);

        await interaction.showModal(modal);
    },

    async handleManageModal(interaction) {
        const codeName = interaction.fields.getTextInputValue('manage_code_name').trim();
        const action = interaction.fields.getTextInputValue('manage_action').trim().toLowerCase();

        if (!codeName) {
            await interaction.reply({
                content: '❌ Nome do código é obrigatório!',
                flags: 64
            });
            return;
        }

        const codes = getAllPromoCodes();
        if (!codes[codeName]) {
            await interaction.reply({
                content: `❌ Código "${codeName}" não encontrado!`,
                flags: 64
            });
            return;
        }

        if (action === 'remover') {
            const result = removePromoCode(codeName);

            const resultEmbed = new EmbedBuilder()
                .setTitle(result.success ? '✅ Código Removido' : '❌ Erro')
                .setDescription(result.message)
                .setColor(result.success ? 0x00FF00 : 0xFF0000);

            const backButton = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dashboard_back')
                        .setLabel('Voltar ao Dashboard')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.reply({
                embeds: [resultEmbed],
                components: [backButton]
            });
        } else if (action === 'editar') {
            await this.showEditModal(interaction, codeName);
        } else {
            await interaction.reply({
                content: '❌ Ação inválida! Use "remover" ou "editar".',
                flags: 64
            });
        }
    },

    async showEditModal(interaction, codeName) {
        const codes = getAllPromoCodes();
        const code = codes[codeName];

        const modal = new ModalBuilder()
            .setCustomId(`dashboard_edit_modal_${codeName}`)
            .setTitle(`Editar Código: ${codeName}`);

        const superlockpointsInput = new TextInputBuilder()
            .setCustomId('edit_superlockpoints')
            .setLabel('SuperLockPoints')
            .setStyle(TextInputStyle.Short)
            .setValue(code.rewards.superlockpoints.toString())
            .setRequired(true)
            .setMaxLength(10);

        const luckySpinsInput = new TextInputBuilder()
            .setCustomId('edit_lucky_spins')
            .setLabel('Lucky Spins')
            .setStyle(TextInputStyle.Short)
            .setValue(code.rewards.luckySpins.toString())
            .setRequired(true)
            .setMaxLength(5);

        const mysteryBoxesInput = new TextInputBuilder()
            .setCustomId('edit_mystery_boxes')
            .setLabel('Mystery Boxes')
            .setStyle(TextInputStyle.Short)
            .setValue(code.rewards.mysteryBoxes.toString())
            .setRequired(true)
            .setMaxLength(5);

        const validityInput = new TextInputBuilder()
            .setCustomId('edit_validity_days')
            .setLabel('Validade em dias (0 para sem expiração)')
            .setStyle(TextInputStyle.Short)
            .setValue(code.validityDays.toString())
            .setRequired(true)
            .setMaxLength(5);

        const superlockpointsRow = new ActionRowBuilder().addComponents(superlockpointsInput);
        const luckySpinsRow = new ActionRowBuilder().addComponents(luckySpinsInput);
        const mysteryBoxesRow = new ActionRowBuilder().addComponents(mysteryBoxesInput);
        const validityRow = new ActionRowBuilder().addComponents(validityInput);

        modal.addComponents(superlockpointsRow, luckySpinsRow, mysteryBoxesRow, validityRow);

        await interaction.showModal(modal);
    },

    async handleEditModal(interaction) {
        const codeName = interaction.customId.split('_')[3];
        const superlockpoints = parseInt(interaction.fields.getTextInputValue('edit_superlockpoints')) || 0;
        const luckySpins = parseInt(interaction.fields.getTextInputValue('edit_lucky_spins')) || 0;
        const mysteryBoxes = parseInt(interaction.fields.getTextInputValue('edit_mystery_boxes')) || 0;
        const validityDays = parseInt(interaction.fields.getTextInputValue('edit_validity_days')) || 0;

        if (superlockpoints < 0 || luckySpins < 0 || mysteryBoxes < 0 || validityDays < 0) {
            await interaction.reply({
                content: '❌ Valores não podem ser negativos!',
                flags: 64
            });
            return;
        }

        if (superlockpoints === 0 && luckySpins === 0 && mysteryBoxes === 0) {
            await interaction.reply({
                content: '❌ O código deve ter pelo menos uma recompensa!',
                flags: 64
            });
            return;
        }

        const newRewards = {
            superlockpoints: superlockpoints,
            luckySpins: luckySpins,
            mysteryBoxes: mysteryBoxes
        };

        const result = editPromoCode(codeName, newRewards, validityDays);

        const resultEmbed = new EmbedBuilder()
            .setTitle(result.success ? '✅ Código Editado' : '❌ Erro')
            .setDescription(result.message)
            .setColor(result.success ? 0x00FF00 : 0xFF0000);

        if (result.success) {
            resultEmbed.addFields(
                {
                    name: '🎁 Novas Recompensas',
                    value: [
                        superlockpoints > 0 ? `💰 ${superlockpoints} ${COIN_EMOJI}` : null,
                        luckySpins > 0 ? `🍀 ${luckySpins} Lucky Spins` : null,
                        mysteryBoxes > 0 ? `📦 ${mysteryBoxes} Mystery Boxes` : null
                    ].filter(Boolean).join('\n'),
                    inline: true
                },
                {
                    name: '⏰ Nova Validade',
                    value: validityDays > 0 ? `${validityDays} dias` : 'Sem expiração',
                    inline: true
                }
            );
        }

        const backButton = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('dashboard_back')
                    .setLabel('Voltar ao Dashboard')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.reply({
            embeds: [resultEmbed],
            components: [backButton]
        });
    }
};
