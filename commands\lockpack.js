const { SlashCommandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, AttachmentBuilder } = require('discord.js');
const { getRandomRarity, getLuckyPackRarity, getRandomCard, RARITIES, COIN_EMOJI, getSafeAttachmentName } = require('../config/cards');
const { canUsePackNormal, updateLastPackTime, addCardToPlayer, getCooldownTimeLeft, getPlayerData, addSuperlockPoints } = require('../utils/database');
const fs = require('fs');
const path = require('path');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('lockpack')
        .setDescription('Abra packs de cartas do MeGa Football Community!'),
    
    async execute(interaction) {
        const userId = interaction.user.id;
        
        // Criar embed inicial com opções de pack
        const packEmbed = new EmbedBuilder()
            .setTitle('🎴 MeGa Football Community - Packs')
            .setDescription('Escolha seu pack de cartas!')
            .setColor(0x00AE86)
            .addFields(
                {
                    name: '🍀 Lucky Pack',
                    value: `\`25 ${COIN_EMOJI}\`\n*Apenas cartas Lendárias+*\n*Lendário: 60% | Místico: 30% | World Class: 10%*`,
                    inline: true
                },
                {
                    name: '📦 Pack Normal',
                    value: `\`Gratuito ${COIN_EMOJI}\`\n*Cooldown: 20 minutos*\n*Todas as raridades*`,
                    inline: true
                }
            )
            .setFooter({ text: 'MeGa Football Community', iconURL: interaction.client.user.displayAvatarURL() })
            .setTimestamp();

        // Criar botões
        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('lucky_pack')
                    .setLabel('Lucky Pack - 25 moedas')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('normal_pack')
                    .setLabel('Pack Normal - Gratuito')
                    .setStyle(ButtonStyle.Primary)
            );

        await interaction.reply({ embeds: [packEmbed], components: [row] });
    },

    async handlePackButton(interaction) {
        const userId = interaction.user.id;

        // Verificar se é o usuário que executou o comando original
        const originalUserId = interaction.message.interaction?.user?.id;
        if (originalUserId && userId !== originalUserId) {
            await interaction.reply({
                content: '❌ Apenas quem executou o comando pode interagir com ele!',
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }
        
        if (interaction.customId === 'lucky_pack') {
            // Verificar se o jogador tem SuperLockPoints suficientes
            const playerData = getPlayerData(userId);
            const luckyPackPrice = 25;

            if (playerData.superlockpoints < luckyPackPrice) {
                await interaction.reply({
                    content: `❌ Você precisa de ${luckyPackPrice} ${COIN_EMOJI} para comprar o Lucky Pack! Você tem apenas ${playerData.superlockpoints} ${COIN_EMOJI}.`,
                    flags: 64 // MessageFlags.Ephemeral
                });
                return;
            }

            // Deduzir SuperLockPoints
            addSuperlockPoints(userId, -luckyPackPrice);

            // Sortear carta do Lucky Pack
            const rarity = getLuckyPackRarity();
            const cardName = getRandomCard(rarity);
            const rarityInfo = RARITIES[rarity];

            // Adicionar carta ao jogador
            const card = addCardToPlayer(userId, cardName, rarity);

            // Criar embed da carta obtida
            const cardEmbed = new EmbedBuilder()
                .setTitle('🍀 Lucky Pack - MeGa Football Community')
                .setDescription(`**Pack:** Lucky Pack\n**Valor:** ${luckyPackPrice} ${COIN_EMOJI}\n**Raridade:** ${rarityInfo.emoji} ${rarityInfo.name}`)
                .setColor(rarityInfo.color)
                .addFields(
                    {
                        name: '⚽ Jogador Obtido',
                        value: `**${cardName}**`,
                        inline: false
                    },
                    {
                        name: `${COIN_EMOJI} Saldo Restante`,
                        value: `${playerData.superlockpoints - luckyPackPrice} SuperLockPoints`,
                        inline: true
                    }
                )
                .setFooter({
                    text: `Obtido por ${interaction.user.username} • ID: ${card.id}`,
                    iconURL: interaction.user.displayAvatarURL()
                })
                .setTimestamp();

            // Tentar adicionar imagem da carta
            const imagePath = path.join(__dirname, '..', 'images', `${cardName}.jpg`);

            let attachment = null;
            let hasImage = false;

            if (fs.existsSync(imagePath)) {
                try {
                    const safeAttachmentName = getSafeAttachmentName(cardName);
                    attachment = new AttachmentBuilder(imagePath, { name: safeAttachmentName });
                    cardEmbed.setImage(`attachment://${safeAttachmentName}`);
                    hasImage = true;
                } catch (error) {
                    console.log(`Erro ao carregar imagem para ${cardName}:`, error.message);
                    cardEmbed.setThumbnail(interaction.user.displayAvatarURL());
                }
            } else {
                cardEmbed.setThumbnail(interaction.user.displayAvatarURL());
            }

            // Criar botão QuickSell
            const quickSellRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`quicksell_${card.id}`)
                        .setLabel(`QuickSell - ${rarityInfo.price} moedas`)
                        .setStyle(ButtonStyle.Danger)
                );

            const replyOptions = {
                embeds: [cardEmbed],
                components: [quickSellRow],
                files: [], // Sempre limpar arquivos anteriores
                attachments: [] // Limpar anexos
            };

            // Adicionar nova imagem apenas se existir
            if (attachment && hasImage) {
                replyOptions.files = [attachment];
            }

            await interaction.update(replyOptions);
            return;
        }

        if (interaction.customId === 'normal_pack') {
            // Verificar cooldown
            if (!canUsePackNormal(userId)) {
                const timeLeft = getCooldownTimeLeft(userId);
                const minutes = Math.floor(timeLeft / 60000);
                const seconds = Math.floor((timeLeft % 60000) / 1000);
                
                await interaction.reply({
                    content: `⏰ Você precisa esperar mais **${minutes}m ${seconds}s** para usar o Pack Normal novamente!`,
                    flags: 64 // MessageFlags.Ephemeral
                });
                return;
            }

            // Sortear carta
            const rarity = getRandomRarity();
            const cardName = getRandomCard(rarity);
            const rarityInfo = RARITIES[rarity];
            
            // Adicionar carta ao jogador
            const card = addCardToPlayer(userId, cardName, rarity);
            
            // Atualizar cooldown
            updateLastPackTime(userId);
            
            // Criar embed da carta obtida
            const cardEmbed = new EmbedBuilder()
                .setTitle('📦 Pack Normal - MeGa Football Community')
                .setDescription(`**Pack:** Pack Normal\n**Valor:** Gratuito ${COIN_EMOJI}\n**Raridade:** ${rarityInfo.emoji} ${rarityInfo.name}`)
                .setColor(rarityInfo.color)
                .addFields(
                    {
                        name: '⚽ Jogador Obtido',
                        value: `**${cardName}**`,
                        inline: false
                    }
                )
                .setFooter({
                    text: `Obtido por ${interaction.user.username} • ID: ${card.id}`,
                    iconURL: interaction.user.displayAvatarURL()
                })
                .setTimestamp();

            // Tentar adicionar imagem da carta
            const imagePath = path.join(__dirname, '..', 'images', `${cardName}.jpg`);

            let attachment = null;
            let hasImage = false;

            if (fs.existsSync(imagePath)) {
                try {
                    const safeAttachmentName = getSafeAttachmentName(cardName);
                    attachment = new AttachmentBuilder(imagePath, { name: safeAttachmentName });
                    cardEmbed.setImage(`attachment://${safeAttachmentName}`);
                    hasImage = true;
                } catch (error) {
                    console.log(`Erro ao carregar imagem para ${cardName}:`, error.message);
                    cardEmbed.setThumbnail(interaction.user.displayAvatarURL());
                }
            } else {
                cardEmbed.setThumbnail(interaction.user.displayAvatarURL());
            }

            // Criar botão QuickSell
            const quickSellRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`quicksell_${card.id}`)
                        .setLabel(`QuickSell - ${rarityInfo.price} moedas`)
                        .setStyle(ButtonStyle.Danger)
                );

            const replyOptions = {
                embeds: [cardEmbed],
                components: [quickSellRow],
                files: [], // Sempre limpar arquivos anteriores
                attachments: [] // Limpar anexos
            };

            // Adicionar nova imagem apenas se existir
            if (attachment && hasImage) {
                replyOptions.files = [attachment];
            }

            await interaction.update(replyOptions);
        }
    }
};
