const { <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er, Embed<PERSON><PERSON>er, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const { getPlayerData, getPlayerVip, canUseLuckySpin, canUseVipPack } = require('../utils/database');
const { VIP_TYPES, getPackCooldown } = require('../config/vip');
const { COIN_EMOJI } = require('../config/cards');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('vipstatus')
        .setDescription('📊 Veja seu status VIP e benefícios!')
        .addUserOption(option =>
            option.setName('jogador')
                .setDescription('Ver status VIP de outro jogador (opcional)')
                .setRequired(false)),
    
    async execute(interaction) {
        const targetUser = interaction.options.getUser('jogador') || interaction.user;
        const userId = targetUser.id;
        
        const playerData = getPlayerData(userId);
        const vipData = getPlayerVip(userId);
        const vipInfo = VIP_TYPES[vipData.type] || VIP_TYPES.NONE;
        
        // Calcular tempo desde a compra
        let purchaseInfo = '';
        if (vipData.purchaseDate) {
            const daysSincePurchase = Math.floor((Date.now() - vipData.purchaseDate) / (1000 * 60 * 60 * 24));
            purchaseInfo = `\n🗓️ **VIP desde:** ${daysSincePurchase} dia(s) atrás`;
        }
        
        // Informações de cooldown
        const baseCooldown = 20; // minutos
        const vipCooldown = Math.floor(getPackCooldown(vipData.type) / (1000 * 60)); // converter para minutos
        const cooldownReduction = baseCooldown - vipCooldown;
        
        const vipEmbed = new EmbedBuilder()
            .setTitle(`${vipInfo.emoji} Status VIP - ${targetUser.username}`)
            .setDescription(`**VIP Atual:** ${vipInfo.name}${purchaseInfo}`)
            .setColor(vipInfo.color)
            .setThumbnail(targetUser.displayAvatarURL())
            .addFields(
                {
                    name: '💰 Saldo Atual',
                    value: `${playerData.superlockpoints} ${COIN_EMOJI}`,
                    inline: true
                },
                {
                    name: '⏰ Cooldown do Pack',
                    value: `${vipCooldown} minutos ${cooldownReduction > 0 ? `(-${cooldownReduction}min)` : ''}`,
                    inline: true
                },
                {
                    name: '🎴 Total de Cartas',
                    value: `${playerData.cards.length} cartas`,
                    inline: true
                }
            );

        // Adicionar benefícios do VIP
        if (vipInfo.benefits && vipInfo.benefits.length > 0) {
            vipEmbed.addFields({
                name: '🎁 Benefícios Ativos',
                value: vipInfo.benefits.map(benefit => `• ${benefit}`).join('\n'),
                inline: false
            });
        }

        // Adicionar status de benefícios especiais
        const specialBenefits = [];
        
        if (vipData.type === 'GALAXIAL' || vipData.type === 'MEGAVIP' || vipData.type === 'EMPEROR') {
            const canSpin = canUseLuckySpin(userId);
            if (vipData.type === 'GALAXIAL') {
                const nextSpinTime = new Date(vipData.lastLuckySpinTime + (4 * 24 * 60 * 60 * 1000));
                specialBenefits.push(`🎰 Lucky Spin: ${canSpin ? '✅ Disponível' : `❌ Próximo: ${nextSpinTime.toLocaleDateString()}`}`);
            } else if (vipData.type === 'MEGAVIP') {
                specialBenefits.push(`🎰 Lucky Spins: ${3 - (vipData.dailyLuckySpinsUsed || 0)}/3 hoje`);
            } else if (vipData.type === 'EMPEROR') {
                specialBenefits.push(`🎰 Lucky Spins: ${2 - (vipData.dailyLuckySpinsUsed || 0)}/2 hoje`);
            }
        }
        
        if (vipData.type === 'MEGAVIP') {
            const canVipPack = canUseVipPack(userId);
            const nextPackTime = new Date(vipData.lastVipPackTime + (24 * 60 * 60 * 1000));
            specialBenefits.push(`📦 Pack VIP Diário: ${canVipPack ? '✅ Disponível' : `❌ Próximo: ${nextPackTime.toLocaleDateString()}`}`);
        }

        if (specialBenefits.length > 0) {
            vipEmbed.addFields({
                name: '⭐ Status dos Benefícios',
                value: specialBenefits.join('\n'),
                inline: false
            });
        }

        // Adicionar informação sobre upgrade se não for o VIP mais alto
        if (vipData.type !== 'BUSINESS' && vipData.type !== 'MEGAVIP') {
            const nextVips = Object.entries(VIP_TYPES)
                .filter(([key, vip]) => key !== 'NONE' && vip.price > vipInfo.price)
                .sort(([, a], [, b]) => a.price - b.price)
                .slice(0, 2);

            if (nextVips.length > 0) {
                const upgradeInfo = nextVips.map(([key, vip]) => 
                    `${vip.emoji} **${vip.name}** - ${vip.price} ${COIN_EMOJI}`
                ).join('\n');

                vipEmbed.addFields({
                    name: '⬆️ Próximos Upgrades',
                    value: upgradeInfo,
                    inline: false
                });
            }
        }

        // Footer com informações adicionais
        if (vipData.type === 'NONE') {
            vipEmbed.setFooter({ 
                text: 'Use /shop para comprar seu primeiro VIP!', 
                iconURL: interaction.client.user.displayAvatarURL() 
            });
        } else {
            vipEmbed.setFooter({ 
                text: 'MeGa Football Community - Sistema VIP', 
                iconURL: interaction.client.user.displayAvatarURL() 
            });
        }

        vipEmbed.setTimestamp();

        // Adicionar botões se tiver VIP ativo
        const components = [];
        if (vipData.type !== 'NONE') {
            const actionRow = new ActionRowBuilder();

            // Botão de benefícios sempre disponível (mostra automáticos ou especiais)
            actionRow.addComponents(
                new ButtonBuilder()
                    .setCustomId('vip_show_benefits')
                    .setLabel('🎁 Ver Benefícios')
                    .setStyle(ButtonStyle.Success)
            );

            // Botão de upgrade apenas se não for o VIP mais alto
            if (vipData.type !== 'BUSINESS' && vipData.type !== 'MEGAVIP') {
                actionRow.addComponents(
                    new ButtonBuilder()
                        .setCustomId('shop_vips')
                        .setLabel('⬆️ Upgrade VIP')
                        .setStyle(ButtonStyle.Primary)
                );
            }

            components.push(actionRow);
        }

        const replyOptions = {
            embeds: [vipEmbed],
            files: [], // Limpar arquivos
            attachments: [] // Limpar anexos
        };
        if (components.length > 0) {
            replyOptions.components = components;
        }

        await interaction.reply(replyOptions);
    }
};
