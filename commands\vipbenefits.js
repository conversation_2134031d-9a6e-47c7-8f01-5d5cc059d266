const { <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er, Embed<PERSON><PERSON>er, ActionRowBuilder, ButtonBuilder, ButtonStyle, AttachmentBuilder } = require('discord.js');
const { getPlayerData, getPlayerVip, canUseLuckySpin, useLuckySpin, canUseVipPack, useVipPack, addCardToPlayer } = require('../utils/database');
const { VIP_TYPES } = require('../config/vip');
const { COIN_EMOJI, getRandomCard, RARITIES } = require('../config/cards');
const fs = require('fs');
const path = require('path');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('vipbenefits')
        .setDescription('🎁 Use seus benefícios VIP!')
        .addStringOption(option =>
            option.setName('beneficio')
                .setDescription('Qual benefício você quer usar?')
                .setRequired(false)
                .addChoices(
                    { name: '🎰 Lucky Spin', value: 'luckyspin' },
                    { name: '📦 Pack VIP Diário', value: 'vippack' },
                    { name: '📋 Ver Benefícios', value: 'list' }
                )),

    async execute(interaction) {
        const userId = interaction.user.id;
        const beneficio = interaction.options.getString('beneficio') || 'list';
        
        const playerData = getPlayerData(userId);
        const vipData = getPlayerVip(userId);
        const vipInfo = VIP_TYPES[vipData.type] || VIP_TYPES.NONE;

        if (vipData.type === 'NONE') {
            const noVipEmbed = new EmbedBuilder()
                .setTitle('❌ Sem VIP')
                .setDescription('Você não possui nenhum VIP ativo!\n\n🛒 Use `/shop` → VIPs para adquirir um status VIP e desbloquear benefícios incríveis!')
                .setColor(0x95A5A6)
                .setFooter({ 
                    text: 'MeGa Football Community - Sistema VIP', 
                    iconURL: interaction.user.displayAvatarURL() 
                });

            await interaction.reply({ embeds: [noVipEmbed] });
            return;
        }

        if (beneficio === 'list') {
            await this.showVipBenefits(interaction, vipData, vipInfo);
        } else if (beneficio === 'luckyspin') {
            await this.useLuckySpin(interaction, userId, vipData, vipInfo);
        } else if (beneficio === 'vippack') {
            await this.useVipPack(interaction, userId, vipData, vipInfo);
        }
    },

    async showVipBenefits(interaction, vipData, vipInfo) {
        const userId = interaction.user.id;
        const playerData = getPlayerData(userId);

        const benefitsEmbed = new EmbedBuilder()
            .setTitle(`${vipInfo.emoji} Seus Benefícios VIP`)
            .setDescription(`**VIP Atual:** ${vipInfo.name}\n💰 **Saldo:** ${playerData.superlockpoints} ${COIN_EMOJI}`)
            .setColor(vipInfo.color)
            .setThumbnail(interaction.user.displayAvatarURL())
            .addFields(
                {
                    name: '🎁 Benefícios Ativos',
                    value: vipInfo.benefits.map(benefit => `• ${benefit}`).join('\n'),
                    inline: false
                }
            );

        // Verificar benefícios especiais disponíveis
        const specialBenefits = [];
        const components = [];

        // Lucky Spin
        if (vipData.type === 'GALAXIAL' || vipData.type === 'MEGAVIP' || vipData.type === 'EMPEROR') {
            const canSpin = canUseLuckySpin(userId);
            
            if (vipData.type === 'GALAXIAL') {
                const nextSpinTime = new Date(vipData.lastLuckySpinTime + (4 * 24 * 60 * 60 * 1000));
                specialBenefits.push(`🎰 **Lucky Spin:** ${canSpin ? '✅ Disponível' : `❌ Próximo: ${nextSpinTime.toLocaleDateString()}`}`);
            } else if (vipData.type === 'MEGAVIP') {
                specialBenefits.push(`🎰 **Lucky Spins:** ${3 - (vipData.dailyLuckySpinsUsed || 0)}/3 hoje`);
            } else if (vipData.type === 'EMPEROR') {
                specialBenefits.push(`🎰 **Lucky Spins:** ${2 - (vipData.dailyLuckySpinsUsed || 0)}/2 hoje`);
            }

            if (canSpin) {
                if (!components[0]) components[0] = new ActionRowBuilder();
                components[0].addComponents(
                    new ButtonBuilder()
                        .setCustomId('vip_use_luckyspin')
                        .setLabel('🎰 Usar Lucky Spin')
                        .setStyle(ButtonStyle.Success)
                );
            }
        }

        // Pack VIP
        if (vipData.type === 'MEGAVIP') {
            const canVipPack = canUseVipPack(userId);
            const nextPackTime = new Date(vipData.lastVipPackTime + (24 * 60 * 60 * 1000));
            specialBenefits.push(`📦 **Pack VIP Diário:** ${canVipPack ? '✅ Disponível' : `❌ Próximo: ${nextPackTime.toLocaleDateString()}`}`);

            if (canVipPack) {
                if (!components[0]) components[0] = new ActionRowBuilder();
                components[0].addComponents(
                    new ButtonBuilder()
                        .setCustomId('vip_use_vippack')
                        .setLabel('📦 Usar Pack VIP')
                        .setStyle(ButtonStyle.Primary)
                );
            }
        }

        if (specialBenefits.length > 0) {
            benefitsEmbed.addFields({
                name: '⭐ Benefícios Especiais',
                value: specialBenefits.join('\n'),
                inline: false
            });
        }

        benefitsEmbed.setFooter({ 
            text: 'Use os botões para ativar benefícios disponíveis', 
            iconURL: interaction.client.user.displayAvatarURL() 
        });

        const replyOptions = {
            embeds: [benefitsEmbed],
            files: [], // Limpar arquivos
            attachments: [] // Limpar anexos
        };
        if (components.length > 0) {
            replyOptions.components = components;
        }

        await interaction.reply(replyOptions);
    },

    async useLuckySpin(interaction, userId, vipData, vipInfo) {
        if (!canUseLuckySpin(userId)) {
            await interaction.reply({
                content: '❌ **Lucky Spin não disponível!**\n\nVerifique seu status VIP e tempo de cooldown.',
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        // Usar o Lucky Spin
        useLuckySpin(userId);

        // Gerar carta especial (apenas raridades altas)
        const luckyRarities = ['LENDARIO', 'MISTICO', 'WORLD_CLASS'];
        const rarity = luckyRarities[Math.floor(Math.random() * luckyRarities.length)];
        const cardName = getRandomCard(rarity);
        const card = addCardToPlayer(userId, cardName, rarity);
        const rarityInfo = RARITIES[rarity];

        const spinEmbed = new EmbedBuilder()
            .setTitle('🎰 Lucky Spin Usado!')
            .setDescription(`${vipInfo.emoji} **${vipInfo.name}** ativou um Lucky Spin!\n\n🎴 **Carta Obtida:** ${cardName}\n${rarityInfo.emoji} **${rarityInfo.name}**\n💰 **Valor:** ${rarityInfo.price} ${COIN_EMOJI}`)
            .setColor(rarityInfo.color)
            .addFields(
                {
                    name: '🎁 Benefício Usado',
                    value: `Lucky Spin do ${vipInfo.name}`,
                    inline: true
                },
                {
                    name: '🆔 ID da Carta',
                    value: `${card.id}`,
                    inline: true
                }
            )
            .setFooter({
                text: `Lucky Spin usado por ${interaction.user.username}`,
                iconURL: interaction.user.displayAvatarURL()
            })
            .setTimestamp();

        // Tentar adicionar imagem da carta
        const imagePath = path.join(__dirname, '..', 'images', `${cardName}.jpg`);
        let attachment = null;
        let hasImage = false;

        if (fs.existsSync(imagePath)) {
            try {
                attachment = new AttachmentBuilder(imagePath, { name: `${cardName}.jpg` });
                spinEmbed.setImage(`attachment://${cardName}.jpg`);
                hasImage = true;
            } catch (error) {
                console.log(`Erro ao carregar imagem para ${cardName}:`, error.message);
                spinEmbed.setThumbnail(interaction.user.displayAvatarURL());
            }
        } else {
            spinEmbed.setThumbnail(interaction.user.displayAvatarURL());
        }

        const replyOptions = {
            embeds: [spinEmbed],
            files: [], // Limpar arquivos
            attachments: [] // Limpar anexos
        };
        if (attachment && hasImage) {
            replyOptions.files = [attachment];
        }

        await interaction.reply(replyOptions);
    },

    async useVipPack(interaction, userId, vipData, vipInfo) {
        if (!canUseVipPack(userId)) {
            await interaction.reply({
                content: '❌ **Pack VIP não disponível!**\n\nVerifique se você é MegaVIP e se já não usou hoje.',
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        // Usar o Pack VIP
        useVipPack(userId);

        // Gerar 3 cartas especiais
        const cards = [];
        for (let i = 0; i < 3; i++) {
            const rarity = this.getVipPackRarity();
            const cardName = getRandomCard(rarity);
            const card = addCardToPlayer(userId, cardName, rarity);
            cards.push({ ...card, rarityInfo: RARITIES[rarity] });
        }

        const totalValue = cards.reduce((sum, card) => sum + card.rarityInfo.price, 0);

        const packEmbed = new EmbedBuilder()
            .setTitle('📦 Pack VIP Usado!')
            .setDescription(`${vipInfo.emoji} **${vipInfo.name}** ativou um Pack VIP Diário!\n\n🎴 **3 Cartas Obtidas**\n💰 **Valor Total:** ${totalValue} ${COIN_EMOJI}`)
            .setColor(vipInfo.color)
            .addFields(
                {
                    name: '🎴 Cartas Obtidas',
                    value: cards.map(card => 
                        `${card.rarityInfo.emoji} **${card.name}** (${card.rarityInfo.price} ${COIN_EMOJI})`
                    ).join('\n'),
                    inline: false
                }
            )
            .setThumbnail(interaction.user.displayAvatarURL())
            .setFooter({ 
                text: `Pack VIP usado por ${interaction.user.username}`, 
                iconURL: interaction.user.displayAvatarURL() 
            })
            .setTimestamp();

        // SEMPRE limpar imagens anteriores e garantir que não há anexos
        const replyOptions = {
            embeds: [packEmbed],
            files: [], // Limpar arquivos
            attachments: [] // Limpar anexos
        };

        await interaction.reply(replyOptions);
    },

    getVipPackRarity() {
        // Sistema de raridade para Pack VIP (melhor que boxes normais)
        const vipRarities = [
            'RARO', 'RARO', // 40% raro
            'LENDARIO', 'LENDARIO', 'LENDARIO', // 60% lendário
            'MISTICO' // 20% místico (muito raro)
        ];
        
        return vipRarities[Math.floor(Math.random() * vipRarities.length)];
    },

    async handleVipButtons(interaction) {
        const userId = interaction.user.id;
        
        // Verificar se é o usuário que executou o comando original
        const originalUserId = interaction.message.interaction?.user?.id;
        if (originalUserId && userId !== originalUserId) {
            await interaction.reply({
                content: '❌ Apenas quem executou o comando pode interagir com ele!',
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        const customId = interaction.customId;

        if (customId === 'vip_use_luckyspin') {
            const vipData = getPlayerVip(userId);
            const vipInfo = VIP_TYPES[vipData.type];
            await this.useLuckySpin(interaction, userId, vipData, vipInfo);
        } else if (customId === 'vip_use_vippack') {
            const vipData = getPlayerVip(userId);
            const vipInfo = VIP_TYPES[vipData.type];
            await this.useVipPack(interaction, userId, vipData, vipInfo);
        }
    }
};
