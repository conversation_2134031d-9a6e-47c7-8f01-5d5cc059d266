const { <PERSON>lash<PERSON>ommandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');
const { usePromoCode, getAllPromoCodes, cleanExpiredCodes } = require('../utils/database');
const { COIN_EMOJI } = require('../config/cards');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('code')
        .setDescription('🎁 Resgatar código promocional do MeGa Football Community'),

    async execute(interaction) {
        // Limpar códigos expirados primeiro
        cleanExpiredCodes();
        
        await this.showCodeInterface(interaction);
    },

    async showCodeInterface(interaction) {
        const codes = getAllPromoCodes();
        const activeCodes = Object.values(codes).filter(code => code.active && (!code.expirationTime || Date.now() < code.expirationTime));

        const codeEmbed = new EmbedBuilder()
            .setTitle('🎁 Resgatar Código Promocional')
            .setDescription('Digite um código promocional para resgatar suas recompensas!\n\n💡 **Como usar:**\n• Clique no botão abaixo\n• Digite o código exato\n• Receba suas recompensas!')
            .setColor(0x9B59B6)
            .setFooter({
                text: 'MeGa Football Community - Sistema de Códigos',
                iconURL: interaction.client.user.displayAvatarURL()
            })
            .setTimestamp();

        if (activeCodes.length > 0) {
            codeEmbed.addFields({
                name: '📊 Códigos Disponíveis',
                value: `✅ ${activeCodes.length} código(s) ativo(s)\n🎯 Digite o código exato para resgatar`,
                inline: false
            });
        } else {
            codeEmbed.addFields({
                name: '📊 Status',
                value: '❌ Nenhum código promocional ativo no momento',
                inline: false
            });
        }

        const buttonRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('code_redeem')
                    .setLabel('Resgatar Código')
                    .setStyle(ButtonStyle.Success)
                    .setDisabled(activeCodes.length === 0)
            );

        const replyOptions = {
            embeds: [codeEmbed],
            components: [buttonRow],
            files: [],
            attachments: []
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.editReply(replyOptions);
        } else {
            await interaction.reply(replyOptions);
        }
    },

    async handleCodeButtons(interaction) {
        if (interaction.customId === 'code_redeem') {
            await this.showRedeemModal(interaction);
        }
    },

    async showRedeemModal(interaction) {
        const modal = new ModalBuilder()
            .setCustomId('code_redeem_modal')
            .setTitle('Resgatar Código Promocional');

        const codeInput = new TextInputBuilder()
            .setCustomId('promo_code')
            .setLabel('Código Promocional')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Ex: 900kmembers')
            .setRequired(true)
            .setMaxLength(50);

        const codeRow = new ActionRowBuilder().addComponents(codeInput);
        modal.addComponents(codeRow);

        await interaction.showModal(modal);
    },

    async handleRedeemModal(interaction) {
        const promoCode = interaction.fields.getTextInputValue('promo_code').trim();
        const userId = interaction.user.id;

        if (!promoCode) {
            await interaction.reply({
                content: '❌ Código promocional é obrigatório!',
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        // Tentar usar o código
        const result = usePromoCode(userId, promoCode);

        if (result.success) {
            // Sucesso - mostrar recompensas
            const successEmbed = new EmbedBuilder()
                .setTitle('🎉 Código Resgatado com Sucesso!')
                .setDescription(`**Parabéns!** Você resgatou o código **"${promoCode}"**!`)
                .setColor(0x00FF00)
                .addFields(
                    {
                        name: '🎁 Suas Recompensas',
                        value: result.rewards.length > 0 ? result.rewards.map(reward => `• ${reward}`).join('\n') : 'Nenhuma recompensa',
                        inline: false
                    },
                    {
                        name: '📊 Informações do Código',
                        value: `• **Nome:** ${result.code.name}\n• **Usado:** ${result.code.usedCount}x\n• **Criado:** <t:${Math.floor(result.code.createdAt / 1000)}:R>`,
                        inline: false
                    }
                )
                .setFooter({
                    text: `Resgatado por ${interaction.user.username}`,
                    iconURL: interaction.user.displayAvatarURL()
                })
                .setTimestamp();

            // Adicionar informação de expiração se houver
            if (result.code.expirationTime) {
                successEmbed.addFields({
                    name: '⏰ Expiração',
                    value: `Expira: <t:${Math.floor(result.code.expirationTime / 1000)}:R>`,
                    inline: true
                });
            }

            const newCodeButton = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('code_redeem')
                        .setLabel('Resgatar Outro Código')
                        .setStyle(ButtonStyle.Primary)
                );

            await interaction.reply({
                embeds: [successEmbed],
                components: [newCodeButton]
            });
        } else {
            // Erro - mostrar mensagem de erro
            let errorColor = 0xFF0000;
            let errorTitle = '❌ Erro ao Resgatar Código';
            
            // Personalizar mensagem baseada no tipo de erro
            if (result.message.includes('já usou')) {
                errorColor = 0xFFA500;
                errorTitle = '⚠️ Código Já Utilizado';
            } else if (result.message.includes('expirado')) {
                errorColor = 0x808080;
                errorTitle = '⏰ Código Expirado';
            } else if (result.message.includes('não encontrado')) {
                errorColor = 0xFF6B6B;
                errorTitle = '🔍 Código Não Encontrado';
            }

            const errorEmbed = new EmbedBuilder()
                .setTitle(errorTitle)
                .setDescription(`**Código:** "${promoCode}"\n**Erro:** ${result.message}`)
                .setColor(errorColor)
                .addFields({
                    name: '💡 Dicas',
                    value: '• Verifique se digitou o código corretamente\n• Códigos são sensíveis a maiúsculas/minúsculas\n• Alguns códigos podem ter expirado\n• Você só pode usar cada código uma vez',
                    inline: false
                })
                .setFooter({
                    text: `Tentativa de ${interaction.user.username}`,
                    iconURL: interaction.user.displayAvatarURL()
                })
                .setTimestamp();

            const tryAgainButton = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('code_redeem')
                        .setLabel('Tentar Novamente')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.reply({
                embeds: [errorEmbed],
                components: [tryAgainButton]
            });
        }
    }
};
