# 🎴 MeGa Football Community Bot

Bot de Discord para cartas de futebol com sistema de economia baseado em SuperLockPoints.

## 🚀 Funcionalidades

- **📦 Sistema de Packs**: Abra packs normais com cooldown de 20 minutos
- **🎴 Cartas com Raridades**: 6 níveis de raridade (Comum a World Class)
- **💰 Sistema de Economia**: SuperLockPoints obtidos vendendo cartas
- **🎒 Inventário**: Visualize suas cartas organizadas por raridade
- **⚡ QuickSell**: Venda cartas rapidamente direto dos packs

## 🎯 Comandos

### `/lockpack`
Abre o menu de packs de cartas com duas opções:
- **Lucky Pack**: Em desenvolvimento (desabilitado)
- **Pack Normal**: Gratuito com cooldown de 20 minutos

### `/sell <carta_id>`
Vende uma carta específica do seu inventário usando o ID da carta.

### `/inv [jogador]`
Mostra o inventário de cartas (seu ou de outro jogador).

## 🎨 Raridades e Preços

| Raridade | Emoji | Preço | Chance |
|----------|-------|-------|--------|
| Comum | ⚪ | 5 🪙 | 45% |
| Incomum | 🟢 | 10 🪙 | 30% |
| Raro | 🔵 | 15 🪙 | 15% |
| Lendário | 🟣 | 30 🪙 | 7% |
| Místico | 🔮 | 50 🪙 | 2.5% |
| World Class | 👑 | 60 🪙 | 0.5% |

## 📋 Pré-requisitos

- Node.js 16.9.0 ou superior
- npm ou yarn
- Bot do Discord configurado

## 🛠️ Instalação

1. **Clone o repositório**
```bash
git clone <url-do-repositorio>
cd bot-klayver
```

2. **Instale as dependências**
```bash
npm install
```

3. **Configure as variáveis de ambiente**
```bash
cp .env.example .env
```

Edite o arquivo `.env` com suas informações:
```env
DISCORD_TOKEN=seu_token_do_bot
CLIENT_ID=id_da_aplicacao_discord
GUILD_ID=id_do_servidor_discord
```

4. **Execute o bot**
```bash
npm start
```

Para desenvolvimento com auto-reload:
```bash
npm run dev
```

## 🎮 Como Usar

1. **Convide o bot** para seu servidor Discord
2. **Use `/lockpack`** para abrir seu primeiro pack
3. **Clique em "Pack Normal"** para sortear uma carta
4. **Use "QuickSell"** para vender imediatamente ou guarde no inventário
5. **Use `/inv`** para ver suas cartas
6. **Use `/sell <id>`** para vender cartas específicas

## 📁 Estrutura do Projeto

```
bot-klayver/
├── commands/           # Comandos do bot
│   ├── lockpack.js    # Sistema de packs
│   ├── sell.js        # Sistema de vendas
│   └── inventory.js   # Inventário
├── config/            # Configurações
│   └── cards.js       # Cartas e raridades
├── utils/             # Utilitários
│   └── database.js    # Sistema de dados
├── images/            # Imagens das cartas (.jpg)
├── data/              # Dados dos jogadores (criado automaticamente)
├── index.js           # Arquivo principal
├── package.json       # Dependências
└── .env.example       # Exemplo de configuração
```

## 🎴 Jogadores por Raridade

### Comum (⚪)
Okuhito, Kyora, Tokimitsu, Kurona, KUON, Ishikari, Nanase, IGAGURI, Gagamaru

### Incomum (🟢)
Palmer, Gyokeres, Mitoma, Messi, Zico, C.ronaldo

### Raro (🔵)
Isagi, KUNIGAMI, Nel Nagi, Chigiri, Nikko

### Lendário (🟣)
Karasu, Bachira, REO, Nagi, Mbappé, Raichi, gagamaru (88 over)

### Místico (🔮)
YUKIMIA, Shidou, BAROU, Rin Itoshi

### World Class (👑)
Loki, SAE Itoshi

## 🔧 Configuração do Bot Discord

1. Acesse o [Discord Developer Portal](https://discord.com/developers/applications)
2. Crie uma nova aplicação
3. Vá para "Bot" e crie um bot
4. Copie o token e adicione ao `.env`
5. Em "OAuth2 > URL Generator":
   - Scopes: `bot`, `applications.commands`
   - Permissions: `Send Messages`, `Use Slash Commands`, `Embed Links`, `Attach Files`

## 📝 Notas

- As imagens das cartas devem estar na pasta `images/` com nomes em minúsculo e extensão `.jpg`
- Os dados dos jogadores são salvos em `data/players.json`
- O cooldown do Pack Normal é de 20 minutos
- O sistema usa SuperLockPoints (🪙) como moeda

## 🤝 Contribuição

Sinta-se à vontade para contribuir com melhorias, correções de bugs ou novas funcionalidades!

## 📄 Licença

MIT License - veja o arquivo LICENSE para detalhes.
