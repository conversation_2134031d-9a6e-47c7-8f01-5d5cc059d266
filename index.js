require('dotenv').config();
const { Client, GatewayIntentBits, Collection, REST, Routes } = require('discord.js');
const fs = require('fs');
const path = require('path');

// Criar cliente do Discord
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent
    ]
});

// Coleção de comandos
client.commands = new Collection();

// Carregar comandos
const commandsPath = path.join(__dirname, 'commands');
const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));

const commands = [];

for (const file of commandFiles) {
    const filePath = path.join(commandsPath, file);
    const command = require(filePath);
    
    if ('data' in command && 'execute' in command) {
        client.commands.set(command.data.name, command);
        commands.push(command.data.toJSON());
        console.log(`✅ Comando carregado: ${command.data.name}`);
    } else {
        console.log(`⚠️ Comando em ${filePath} está faltando propriedades "data" ou "execute".`);
    }
}

// Registrar comandos slash
const rest = new REST().setToken(process.env.DISCORD_TOKEN);

(async () => {
    try {
        console.log(`🔄 Registrando ${commands.length} comandos slash...`);

        const data = await rest.put(
            Routes.applicationCommands(process.env.CLIENT_ID),
            { body: commands },
        );

        console.log(`✅ ${data.length} comandos slash registrados com sucesso!`);
    } catch (error) {
        console.error('❌ Erro ao registrar comandos:', error);
    }
})();

// Event: Bot pronto
client.once('ready', () => {
    console.log(`🤖 Bot logado como ${client.user.tag}!`);
    console.log(`🎴 MeGa Football Community Bot está online!`);

    // Inicializar sistema da loja
    try {
        const shopCommand = client.commands.get('shop');
        if (shopCommand && shopCommand.initializeShop) {
            shopCommand.initializeShop();
            console.log('🏪 Sistema da loja inicializado com sucesso!');
        }
    } catch (error) {
        console.error('❌ Erro ao inicializar sistema da loja:', error);
    }

    // Inicializar sistema de códigos promocionais
    try {
        const { cleanExpiredCodes } = require('./utils/database');
        const removedCount = cleanExpiredCodes();
        if (removedCount > 0) {
            console.log(`🗑️ ${removedCount} código(s) expirado(s) removido(s) na inicialização`);
        }
        console.log('🎁 Sistema de códigos promocionais inicializado!');
    } catch (error) {
        console.error('❌ Erro ao inicializar sistema de códigos:', error);
    }

    // Definir status do bot
    client.user.setActivity('⚽ MeGa Football Community', { type: 0 }); // 0 = PLAYING
});

// Event: Interação de comando slash
client.on('interactionCreate', async interaction => {
    // Comandos slash
    if (interaction.isChatInputCommand()) {
        const command = client.commands.get(interaction.commandName);

        if (!command) {
            console.error(`❌ Comando ${interaction.commandName} não encontrado.`);
            return;
        }

        try {
            await command.execute(interaction);
        } catch (error) {
            console.error('❌ Erro ao executar comando:', error);

            // Só tentar responder se a interação não foi respondida ainda
            if (!interaction.replied && !interaction.deferred) {
                try {
                    await interaction.reply({
                        content: '❌ Houve um erro ao executar este comando!',
                        flags: 64 // MessageFlags.Ephemeral
                    });
                } catch (replyError) {
                    console.error('❌ Erro ao responder interação:', replyError.message);
                }
            }
        }
    }
    
    // Botões
    else if (interaction.isButton()) {
        try {
            // Botões do lockpack
            if (interaction.customId === 'normal_pack' || interaction.customId === 'lucky_pack') {
                const lockpackCommand = client.commands.get('lockpack');
                if (lockpackCommand && lockpackCommand.handlePackButton) {
                    await lockpackCommand.handlePackButton(interaction);
                }
            }

            // Botões de quicksell
            else if (interaction.customId.startsWith('quicksell_')) {
                const sellCommand = client.commands.get('sell');
                if (sellCommand && sellCommand.handleQuickSell) {
                    await sellCommand.handleQuickSell(interaction);
                }
            }

            // Botões do sistema de vendas
            else if (interaction.customId === 'sell_by_name') {
                const sellCommand = client.commands.get('sell');
                if (sellCommand && sellCommand.handleSellInteraction) {
                    await sellCommand.handleSellInteraction(interaction);
                }
            }

            // Botões da loja
            else if (interaction.customId.startsWith('shop_') ||
                     interaction.customId.startsWith('buy_')) {
                const shopCommand = client.commands.get('shop');
                if (shopCommand && shopCommand.handleShopButtons) {
                    await shopCommand.handleShopButtons(interaction);
                }
            }

            // Botões das boxes
            else if (interaction.customId.startsWith('box_')) {
                const boxCommand = client.commands.get('box');
                if (boxCommand && boxCommand.handleBoxButtons) {
                    await boxCommand.handleBoxButtons(interaction);
                }
            }

            // Botões VIP
            else if (interaction.customId.startsWith('vip_')) {
                if (interaction.customId === 'vip_show_benefits') {
                    // Redirecionar para comando de benefícios
                    const vipCommand = client.commands.get('vipbenefits');
                    if (vipCommand) {
                        await vipCommand.execute(interaction);
                    }
                } else {
                    const vipCommand = client.commands.get('vipbenefits');
                    if (vipCommand && vipCommand.handleVipButtons) {
                        await vipCommand.handleVipButtons(interaction);
                    }
                }
            }

            // Botões do Dashboard de Códigos
            else if (interaction.customId.startsWith('dashboard_')) {
                const dashboardCommand = client.commands.get('codedashboard');
                if (dashboardCommand && dashboardCommand.handleDashboardButtons) {
                    await dashboardCommand.handleDashboardButtons(interaction);
                }
            }

            // Botões do Sistema de Códigos
            else if (interaction.customId.startsWith('code_')) {
                const codeCommand = client.commands.get('code');
                if (codeCommand && codeCommand.handleCodeButtons) {
                    await codeCommand.handleCodeButtons(interaction);
                }
            }
        } catch (error) {
            console.error('❌ Erro ao processar botão:', error);

            // Só tentar responder se a interação não foi respondida ainda
            if (!interaction.replied && !interaction.deferred) {
                try {
                    await interaction.reply({
                        content: '❌ Houve um erro ao processar esta ação!',
                        flags: 64 // MessageFlags.Ephemeral
                    });
                } catch (replyError) {
                    console.error('❌ Erro ao responder interação:', replyError.message);
                }
            }
        }
    }

    // Select Menus
    else if (interaction.isStringSelectMenu()) {
        try {
            if (interaction.customId === 'sell_by_rarity') {
                const sellCommand = client.commands.get('sell');
                if (sellCommand && sellCommand.handleSellInteraction) {
                    await sellCommand.handleSellInteraction(interaction);
                }
            }
        } catch (error) {
            console.error('❌ Erro ao processar select menu:', error);

            // Só tentar responder se a interação não foi respondida ainda
            if (!interaction.replied && !interaction.deferred) {
                try {
                    await interaction.reply({
                        content: '❌ Houve um erro ao processar esta ação!',
                        flags: 64 // MessageFlags.Ephemeral
                    });
                } catch (replyError) {
                    console.error('❌ Erro ao responder interação:', replyError.message);
                }
            }
        }
    }

    // Modals
    else if (interaction.isModalSubmit()) {
        try {
            if (interaction.customId === 'sell_by_name_modal') {
                const sellCommand = client.commands.get('sell');
                if (sellCommand && sellCommand.handleNameSellModal) {
                    await sellCommand.handleNameSellModal(interaction);
                }
            }
            // Modals do Dashboard de Códigos
            else if (interaction.customId === 'dashboard_create_modal') {
                const dashboardCommand = client.commands.get('codedashboard');
                if (dashboardCommand && dashboardCommand.handleCreateModal) {
                    await dashboardCommand.handleCreateModal(interaction);
                }
            }
            else if (interaction.customId === 'dashboard_manage_modal') {
                const dashboardCommand = client.commands.get('codedashboard');
                if (dashboardCommand && dashboardCommand.handleManageModal) {
                    await dashboardCommand.handleManageModal(interaction);
                }
            }
            else if (interaction.customId.startsWith('dashboard_edit_modal_')) {
                const dashboardCommand = client.commands.get('codedashboard');
                if (dashboardCommand && dashboardCommand.handleEditModal) {
                    await dashboardCommand.handleEditModal(interaction);
                }
            }
            // Modal do Sistema de Códigos
            else if (interaction.customId === 'code_redeem_modal') {
                const codeCommand = client.commands.get('code');
                if (codeCommand && codeCommand.handleRedeemModal) {
                    await codeCommand.handleRedeemModal(interaction);
                }
            }
        } catch (error) {
            console.error('❌ Erro ao processar modal:', error);

            // Só tentar responder se a interação não foi respondida ainda
            if (!interaction.replied && !interaction.deferred) {
                try {
                    await interaction.reply({
                        content: '❌ Houve um erro ao processar esta ação!',
                        flags: 64 // MessageFlags.Ephemeral
                    });
                } catch (replyError) {
                    console.error('❌ Erro ao responder interação:', replyError.message);
                }
            }
        }
    }
});

// Event: Erro
client.on('error', error => {
    console.error('❌ Erro do cliente Discord:', error);
});

// Tratamento de erros não capturados
process.on('unhandledRejection', error => {
    console.error('❌ Erro não tratado:', error);
});

// Login do bot
client.login(process.env.DISCORD_TOKEN);
