const { SlashCommandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, AttachmentBuilder, StringSelectMenuBuilder } = require('discord.js');
const { RARITIES, CARDS, COIN_EMOJI, getRandomCard } = require('../config/cards');
const { getPlayerData, addSuperlockPoints, addCardToPlayer, setPlayerVip, getPlayerVip, loadShopData, saveShopData } = require('../utils/database');
const { VIP_TYPES, applyVipDiscount, getVipsSortedByPrice } = require('../config/vip');
const fs = require('fs');
const path = require('path');

// Dados globais da loja (carregados do arquivo de persistência)
let shopData = loadShopData();

module.exports = {
    data: new SlashCommandBuilder()
        .setName('shop')
        .setDescription('🏪 Loja do MeGa Football Community - Players, Packs e VIPs!'),

    // Inicializar sistema da loja no startup
    initializeShop() {
        console.log('🏪 Carregando dados da loja...');

        // Verificar se precisa gerar players iniciais
        if (shopData.players.length === 0 && shopData.lastUpdate === 0) {
            console.log('🏪 Primeira inicialização - gerando players iniciais...');
            this.generateShopPlayers();
            shopData.lastUpdate = Date.now();
            saveShopData(shopData);
        } else {
            console.log(`🏪 Loja carregada com ${shopData.players.length} players disponíveis`);
            console.log(`🏪 Última atualização: ${new Date(shopData.lastUpdate).toLocaleString('pt-BR')}`);
        }

        // Verificar se precisa atualizar
        this.updateShopIfNeeded();
    },
    
    async execute(interaction) {
        // Atualizar loja se necessário
        this.updateShopIfNeeded();
        
        const playerData = getPlayerData(interaction.user.id);

        const shopEmbed = new EmbedBuilder()
            .setTitle('🏪 MeGa Football Community - Loja')
            .setDescription(`Bem-vindo à loja oficial!\n\n💰 **Seu Saldo:** ${playerData.superlockpoints} ${COIN_EMOJI}`)
            .setColor(0x00AE86)
            .addFields(
                {
                    name: '⚽ Loja de Players',
                    value: `• Jogadores especiais únicos\n• Preços especiais\n• Atualiza a cada 4 horas\n• Apenas raridades altas`,
                    inline: true
                },
                {
                    name: '📦 Loja de Packs',
                    value: `• 🍀 Lucky Pack: 25 ${COIN_EMOJI}\n• 💎 Premium Pack: Em breve\n• 👑 Elite Pack: Em breve`,
                    inline: true
                },
                {
                    name: '👑 Loja VIP',
                    value: `• 8 tipos de VIP disponíveis\n• Cooldowns reduzidos\n• Descontos especiais\n• Lucky Spins e mais!`,
                    inline: true
                }
            )
            .setFooter({
                text: 'MeGa Football Community Shop',
                iconURL: interaction.client.user.displayAvatarURL()
            })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('shop_players')
                    .setLabel('Players')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('shop_packs')
                    .setLabel('Packs')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('shop_vips')
                    .setLabel('VIPs')
                    .setStyle(ButtonStyle.Secondary)
            );

        // SEMPRE limpar imagens anteriores e garantir que não há anexos
        const replyOptions = {
            embeds: [shopEmbed],
            components: [row],
            files: [], // Limpar arquivos
            attachments: [] // Limpar anexos
        };

        await interaction.reply(replyOptions);
    },

    updateShopIfNeeded() {
        const now = Date.now();

        if (now - shopData.lastUpdate >= shopData.updateInterval) {
            this.generateShopPlayers();
            shopData.lastUpdate = now;
            saveShopData(shopData); // Salvar dados após atualização
            console.log('🏪 Loja de jogadores atualizada e salva!');
        }
    },

    generateShopPlayers() {
        shopData.players = [];
        shopData.soldPlayers = []; // Resetar lista de vendidos

        // Gerar 6 jogadores aleatórios com preços dinâmicos
        const rarities = ['RARO', 'LENDARIO', 'MISTICO', 'WORLD_CLASS'];

        for (let i = 0; i < 6; i++) {
            const rarity = rarities[Math.floor(Math.random() * rarities.length)];
            const cardName = getRandomCard(rarity);
            const basePrice = RARITIES[rarity].price;

            // Aplicar sistema de inflação dinâmica
            const dynamicPrice = this.applyDynamicPricing(basePrice, rarity);

            shopData.players.push({
                name: cardName,
                rarity: rarity,
                price: dynamicPrice,
                originalPrice: basePrice,
                available: true, // Controle de estoque
                id: `player_${Date.now()}_${i}` // ID único para cada player
            });
        }

        saveShopData(shopData); // Salvar dados após gerar novos players
        console.log('🏪 Loja de jogadores atualizada com novos players e salva!');
    },

    async handleShopButtons(interaction) {
        const userId = interaction.user.id;

        // Verificar se é o usuário que executou o comando original
        const originalUserId = interaction.message.interaction?.user?.id;
        if (originalUserId && userId !== originalUserId) {
            await interaction.reply({
                content: '❌ Apenas quem executou o comando pode interagir com ele!',
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        if (interaction.customId === 'shop_players') {
            await this.showPlayersShop(interaction);
        } else if (interaction.customId === 'shop_packs') {
            await this.showPacksShop(interaction);
        } else if (interaction.customId === 'shop_vips') {
            await this.showVipShop(interaction);
        } else if (interaction.customId === 'shop_main') {
            await this.showMainShop(interaction);
        } else if (interaction.customId.startsWith('buy_player_')) {
            const playerIndex = parseInt(interaction.customId.split('_')[2]);
            await this.buyPlayer(interaction, playerIndex);
        } else if (interaction.customId.startsWith('buy_pack_')) {
            const packType = interaction.customId.split('_')[2];
            await this.buyPack(interaction, packType);
        } else if (interaction.customId.startsWith('buy_vip_')) {
            const vipType = interaction.customId.split('_')[2];
            await this.buyVip(interaction, vipType);
        }
    },

    async showMainShop(interaction) {
        const playerData = getPlayerData(interaction.user.id);

        const shopEmbed = new EmbedBuilder()
            .setTitle('🏪 MeGa Football Community - Loja')
            .setDescription(`Bem-vindo à loja oficial!\n\n💰 **Seu Saldo:** ${playerData.superlockpoints} ${COIN_EMOJI}`)
            .setColor(0x00AE86)
            .addFields(
                {
                    name: '⚽ Loja de Players',
                    value: `• Jogadores especiais únicos\n• Preços especiais\n• Atualiza a cada 4 horas\n• Apenas raridades altas`,
                    inline: true
                },
                {
                    name: '📦 Loja de Packs',
                    value: `• 🍀 Lucky Pack: 25 ${COIN_EMOJI}\n• 💎 Premium Pack: Em breve\n• 👑 Elite Pack: Em breve`,
                    inline: true
                },
                {
                    name: '👑 Loja VIP',
                    value: `• 8 tipos de VIP disponíveis\n• Cooldowns reduzidos\n• Descontos especiais\n• Lucky Spins e mais!`,
                    inline: true
                }
            )
            .setFooter({
                text: 'MeGa Football Community Shop',
                iconURL: interaction.client.user.displayAvatarURL()
            })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('shop_players')
                    .setLabel('Players')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('shop_packs')
                    .setLabel('Packs')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('shop_vips')
                    .setLabel('VIPs')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [shopEmbed], components: [row] });
    },

    async showPlayersShop(interaction) {
        this.updateShopIfNeeded();
        
        const playerData = getPlayerData(interaction.user.id);
        
        const playersEmbed = new EmbedBuilder()
            .setTitle('⚽ Loja de Jogadores Especiais')
            .setDescription(`Jogadores únicos disponíveis por tempo limitado!\n\n💰 **Seu Saldo:** ${playerData.superlockpoints} ${COIN_EMOJI}\n🔄 **Próxima Atualização:** ${this.getNextUpdateTime()}`)
            .setColor(0xFF6B35)
            .setFooter({
                text: 'Clique no botão para comprar • Preços especiais!',
                iconURL: interaction.client.user.displayAvatarURL()
            });

        const vipData = getPlayerVip(interaction.user.id);

        shopData.players.forEach((player, index) => {
            const rarityInfo = RARITIES[player.rarity];
            const originalPrice = player.price;
            const vipPrice = applyVipDiscount(originalPrice, vipData.type, 'player');
            const discount = originalPrice - vipPrice;

            let priceText = `**${vipPrice}** ${COIN_EMOJI}`;
            if (discount > 0) {
                priceText += ` ~~${originalPrice}~~ *(-${discount} VIP)*`;
            }

            // Verificar se está disponível
            const status = player.available ? '' : '\n❌ **VENDIDO**';

            playersEmbed.addFields({
                name: `${rarityInfo.emoji} ${player.name}`,
                value: `${priceText}\n*${rarityInfo.name}*${status}`,
                inline: true
            });
        });

        // Criar botões para comprar jogadores (máximo 5 por linha)
        const rows = [];
        for (let i = 0; i < shopData.players.length; i += 5) {
            const row = new ActionRowBuilder();
            
            for (let j = i; j < Math.min(i + 5, shopData.players.length); j++) {
                const player = shopData.players[j];
                const vipPrice = applyVipDiscount(player.price, vipData.type, 'player');
                const isAvailable = player.available;
                const canAfford = playerData.superlockpoints >= vipPrice;

                row.addComponents(
                    new ButtonBuilder()
                        .setCustomId(`buy_player_${j}`)
                        .setLabel(isAvailable ? `${vipPrice} moedas` : 'VENDIDO')
                        .setStyle(isAvailable ? ButtonStyle.Success : ButtonStyle.Secondary)
                        .setDisabled(!isAvailable || !canAfford)
                );
            }
            
            rows.push(row);
        }

        // Botão para voltar
        const backRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('shop_main')
                    .setLabel('Voltar à Loja')
                    .setStyle(ButtonStyle.Secondary)
            );

        rows.push(backRow);

        // SEMPRE limpar imagens anteriores e garantir que não há anexos
        const replyOptions = {
            embeds: [playersEmbed],
            components: rows,
            files: [], // Limpar arquivos
            attachments: [] // Limpar anexos
        };

        await interaction.update(replyOptions);
    },

    async showPacksShop(interaction) {
        const playerData = getPlayerData(interaction.user.id);
        const vipData = getPlayerVip(interaction.user.id);

        // Obter preço atual da Box com desconto VIP
        const boxBasePrice = this.getBoxPrice();
        const boxVipPrice = applyVipDiscount(boxBasePrice, vipData.type, 'pack');
        const boxDiscount = boxBasePrice - boxVipPrice;

        let boxPriceText = `**${boxVipPrice}** ${COIN_EMOJI}`;
        if (boxDiscount > 0) {
            boxPriceText += ` ~~${boxBasePrice}~~ *(-${boxDiscount} VIP)*`;
        }

        const packsEmbed = new EmbedBuilder()
            .setTitle('📦 Loja de Packs Premium')
            .setDescription(`Packs especiais com múltiplas cartas!\n\n${COIN_EMOJI} **Seu Saldo:** ${playerData.superlockpoints} SuperLockPoints\n🔄 **Próxima Atualização de Preços:** ${this.getNextPackUpdateTime()}`)
            .setColor(0x9B59B6)
            .addFields(
                {
                    name: '🍀 Lucky Pack',
                    value: `**25** ${COIN_EMOJI}\n*1 carta garantida (Lendário+)*\n*Lendário: 60% | Místico: 30% | World Class: 10%*`,
                    inline: true
                },
                {
                    name: '📦 Mystery Box',
                    value: `${boxPriceText}\n*3 cartas garantidas*\n*Preço dinâmico (atualiza a cada 3h)*\n*Use /box para gerenciar*`,
                    inline: true
                },
                {
                    name: '👑 Elite Pack',
                    value: `**200** ${COIN_EMOJI}\n*5 cartas garantidas*\n*Pelo menos 1 Lendário+*\n*Em breve...*`,
                    inline: true
                }
            )
            .setFooter({
                text: 'MeGa Football Community - Packs Shop',
                iconURL: interaction.client.user.displayAvatarURL()
            });

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('buy_pack_lucky')
                    .setLabel('Lucky Pack - 25 moedas')
                    .setStyle(ButtonStyle.Success)
                    .setDisabled(playerData.superlockpoints < 25),
                new ButtonBuilder()
                    .setCustomId('buy_pack_box')
                    .setLabel(`Mystery Box - ${boxVipPrice} moedas`)
                    .setStyle(ButtonStyle.Primary)
                    .setDisabled(playerData.superlockpoints < boxVipPrice),
                new ButtonBuilder()
                    .setCustomId('buy_pack_elite')
                    .setLabel('Elite Pack - Em Breve')
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(true)
            );

        const backRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('shop_main')
                    .setLabel('Voltar à Loja')
                    .setStyle(ButtonStyle.Secondary)
            );

        // SEMPRE limpar imagens anteriores e garantir que não há anexos
        const replyOptions = {
            embeds: [packsEmbed],
            components: [row, backRow],
            files: [], // Limpar arquivos
            attachments: [] // Limpar anexos
        };

        await interaction.update(replyOptions);
    },

    async showVipShop(interaction) {
        const playerData = getPlayerData(interaction.user.id);
        const currentVip = getPlayerVip(interaction.user.id);
        const currentVipInfo = VIP_TYPES[currentVip.type] || VIP_TYPES.NONE;

        const vipEmbed = new EmbedBuilder()
            .setTitle('👑 Loja VIP - MeGa Football Community')
            .setDescription(`Upgrade seu status e desbloqueie benefícios incríveis!\n\n💰 **Seu Saldo:** ${playerData.superlockpoints} ${COIN_EMOJI}\n${currentVipInfo.emoji} **VIP Atual:** ${currentVipInfo.name}`)
            .setColor(0x9B59B6)
            .setFooter({
                text: 'Clique no botão para comprar • VIPs são permanentes!',
                iconURL: interaction.client.user.displayAvatarURL()
            });

        // Adicionar VIPs disponíveis
        const vipsSorted = getVipsSortedByPrice();

        vipsSorted.forEach(([vipKey, vip]) => {
            const isCurrentVip = currentVip.type === vipKey;
            const canAfford = playerData.superlockpoints >= vip.price;
            const status = isCurrentVip ? '✅ **ATIVO**' :
                          canAfford ? '💰 **Disponível**' :
                          '❌ **Insuficiente**';

            vipEmbed.addFields({
                name: `${vip.emoji} ${vip.name} - ${vip.price} ${COIN_EMOJI}`,
                value: `${status}\n${vip.benefits.slice(0, 2).map(b => `• ${b}`).join('\n')}${vip.benefits.length > 2 ? '\n*...e mais benefícios*' : ''}`,
                inline: true
            });
        });

        // Criar botões para compra (máximo 5 por linha)
        const rows = [];
        for (let i = 0; i < vipsSorted.length; i += 5) {
            const row = new ActionRowBuilder();

            for (let j = i; j < Math.min(i + 5, vipsSorted.length); j++) {
                const [vipKey, vip] = vipsSorted[j];
                const isCurrentVip = currentVip.type === vipKey;
                const canAfford = playerData.superlockpoints >= vip.price;

                row.addComponents(
                    new ButtonBuilder()
                        .setCustomId(`buy_vip_${vipKey}`)
                        .setLabel(`${vip.name} - ${vip.price}`)
                        .setStyle(isCurrentVip ? ButtonStyle.Success : ButtonStyle.Primary)
                        .setDisabled(isCurrentVip || !canAfford)
                );
            }

            rows.push(row);
        }

        // Botão para voltar
        const backRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('shop_main')
                    .setLabel('Voltar à Loja')
                    .setStyle(ButtonStyle.Secondary)
            );

        rows.push(backRow);

        // SEMPRE limpar imagens anteriores e garantir que não há anexos
        const replyOptions = {
            embeds: [vipEmbed],
            components: rows,
            files: [], // Limpar arquivos
            attachments: [] // Limpar anexos
        };

        await interaction.update(replyOptions);
    },

    async buyVip(interaction, vipType) {
        const userId = interaction.user.id;
        const vipInfo = VIP_TYPES[vipType];

        if (!vipInfo) {
            await interaction.reply({
                content: '❌ VIP não encontrado!',
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        const playerData = getPlayerData(userId);
        const currentVip = getPlayerVip(userId);

        // Verificar se já tem este VIP
        if (currentVip.type === vipType) {
            await interaction.reply({
                content: `❌ Você já possui o ${vipInfo.name}!`,
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        // Verificar se tem dinheiro suficiente
        if (playerData.superlockpoints < vipInfo.price) {
            await interaction.reply({
                content: `❌ Você não tem ${COIN_EMOJI} suficientes! Precisa de ${vipInfo.price}, mas tem apenas ${playerData.superlockpoints}.`,
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        // Deduzir SuperLockPoints
        addSuperlockPoints(userId, -vipInfo.price);

        // Definir novo VIP
        setPlayerVip(userId, vipType);

        // Criar embed de compra
        const purchaseEmbed = new EmbedBuilder()
            .setTitle('🎉 VIP Adquirido com Sucesso!')
            .setDescription(`**Parabéns!** Você agora é um ${vipInfo.emoji} **${vipInfo.name}**!`)
            .setColor(vipInfo.color)
            .addFields(
                {
                    name: '💰 Valor Pago',
                    value: `${vipInfo.price} ${COIN_EMOJI}`,
                    inline: true
                },
                {
                    name: `${COIN_EMOJI} Saldo Restante`,
                    value: `${playerData.superlockpoints - vipInfo.price} SuperLockPoints`,
                    inline: true
                },
                {
                    name: '🎁 Seus Novos Benefícios',
                    value: vipInfo.benefits.map(benefit => `• ${benefit}`).join('\n'),
                    inline: false
                }
            )
            .setFooter({
                text: `Comprado por ${interaction.user.username} • Use /vipstatus para ver detalhes`,
                iconURL: interaction.user.displayAvatarURL()
            })
            .setTimestamp();

        // SEMPRE limpar imagens anteriores e garantir que não há anexos
        const replyOptions = {
            embeds: [purchaseEmbed],
            components: [],
            files: [], // Limpar arquivos
            attachments: [] // Limpar anexos
        };

        await interaction.update(replyOptions);
    },

    async buyPlayer(interaction, playerIndex) {
        const userId = interaction.user.id;
        const player = shopData.players[playerIndex];

        if (!player) {
            await interaction.reply({
                content: '❌ Jogador não encontrado na loja!',
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        // Verificar se o player ainda está disponível
        if (!player.available) {
            await interaction.reply({
                content: '❌ **Este jogador já foi vendido!**\n\nA loja será atualizada em breve com novos jogadores.',
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        const playerData = getPlayerData(userId);
        const vipData = getPlayerVip(userId);
        const originalPrice = player.price;
        const finalPrice = applyVipDiscount(originalPrice, vipData.type, 'player');
        const discount = originalPrice - finalPrice;

        if (playerData.superlockpoints < finalPrice) {
            await interaction.reply({
                content: `❌ Você não tem ${COIN_EMOJI} suficientes! Precisa de ${finalPrice}, mas tem apenas ${playerData.superlockpoints}.`,
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        // Marcar player como vendido
        player.available = false;
        shopData.soldPlayers.push(playerIndex);
        saveShopData(shopData); // Salvar dados após venda

        // Deduzir SuperLockPoints
        addSuperlockPoints(userId, -finalPrice);

        // Adicionar carta ao jogador
        const card = addCardToPlayer(userId, player.name, player.rarity);
        const rarityInfo = RARITIES[player.rarity];

        // Criar embed de compra
        let priceDisplay = `${finalPrice} ${COIN_EMOJI}`;
        if (discount > 0) {
            priceDisplay += ` ~~${originalPrice}~~ *(-${discount} VIP)*`;
        }

        const purchaseEmbed = new EmbedBuilder()
            .setTitle('🛒 Compra Realizada - Loja de Jogadores')
            .setDescription(`**Jogador:** ${player.name}\n**Raridade:** ${rarityInfo.emoji} ${rarityInfo.name}\n**Preço:** ${priceDisplay}`)
            .setColor(0x00FF00)
            .addFields(
                {
                    name: '💳 Comprador',
                    value: `${interaction.user.username}`,
                    inline: true
                },
                {
                    name: `${COIN_EMOJI} Saldo Restante`,
                    value: `${playerData.superlockpoints - finalPrice} SuperLockPoints`,
                    inline: true
                }
            )
            .setFooter({
                text: `Comprado na loja • ID: ${card.id}`,
                iconURL: interaction.user.displayAvatarURL()
            })
            .setTimestamp();

        // NUNCA adicionar imagens na compra de players - apenas thumbnail do usuário
        purchaseEmbed.setThumbnail(interaction.user.displayAvatarURL());

        // NUNCA adicionar imagens na compra de players - sempre limpar
        const replyOptions = {
            embeds: [purchaseEmbed],
            files: [], // SEMPRE vazio - sem imagens
            attachments: [] // SEMPRE vazio - sem anexos
        };

        await interaction.update(replyOptions);
    },

    async buyPack(interaction, packType) {
        if (packType === 'lucky') {
            // Redirecionar para o sistema de Lucky Pack do lockpack
            await interaction.reply({
                content: '🍀 Use `/lockpack` para comprar Lucky Packs! O sistema é o mesmo.',
                flags: 64 // MessageFlags.Ephemeral
            });
        } else if (packType === 'box') {
            await this.buyBox(interaction);
        } else {
            await interaction.reply({
                content: '📦 Este pack estará disponível em breve!',
                flags: 64 // MessageFlags.Ephemeral
            });
        }
    },

    getNextUpdateTime() {
        const nextUpdate = new Date(shopData.lastUpdate + shopData.updateInterval);
        return nextUpdate.toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit',
            timeZone: 'America/Sao_Paulo'
        });
    },

    // Sistema de inflação dinâmica para players
    applyDynamicPricing(basePrice, rarity) {
        // Fatores de inflação baseados na raridade
        const inflationFactors = {
            'COMUM': { min: 0.8, max: 1.5 },
            'INCOMUM': { min: 0.7, max: 1.8 },
            'RARO': { min: 0.6, max: 2.0 },
            'LENDARIO': { min: 0.5, max: 2.5 },
            'MISTICO': { min: 0.4, max: 3.0 },
            'WORLDCLASS': { min: 0.3, max: 4.0 }
        };

        const factor = inflationFactors[rarity] || inflationFactors['COMUM'];
        const randomMultiplier = Math.random() * (factor.max - factor.min) + factor.min;

        return Math.ceil(basePrice * randomMultiplier);
    },

    // Atualizar preço da Box dinamicamente
    updateBoxPrice() {
        const now = Date.now();
        if (now - shopData.packs.lastPackUpdate >= shopData.packs.packUpdateInterval) {
            // Variação de ±30% no preço base (150)
            const basePrice = 150;
            const variation = (Math.random() - 0.5) * 0.6; // -30% a +30%
            const newPrice = Math.ceil(basePrice * (1 + variation));

            // Limitar entre 80 e 300 moedas
            shopData.packs.boxPrice = Math.max(80, Math.min(300, newPrice));
            shopData.packs.lastPackUpdate = now;
            saveShopData(shopData); // Salvar dados após atualização de preço
            console.log(`📦 Preço da Box atualizado para ${shopData.packs.boxPrice} moedas!`);
        }
    },

    getBoxPrice() {
        this.updateBoxPrice();
        return shopData.packs.boxPrice;
    },

    getNextPackUpdateTime() {
        const nextUpdate = new Date(shopData.packs.lastPackUpdate + shopData.packs.packUpdateInterval);
        return nextUpdate.toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit',
            timeZone: 'America/Sao_Paulo'
        });
    },

    async buyBox(interaction) {
        const userId = interaction.user.id;
        const playerData = getPlayerData(userId);
        const vipData = getPlayerVip(userId);

        const boxBasePrice = this.getBoxPrice();
        const finalPrice = applyVipDiscount(boxBasePrice, vipData.type, 'pack');
        const discount = boxBasePrice - finalPrice;

        if (playerData.superlockpoints < finalPrice) {
            await interaction.reply({
                content: `❌ Você não tem ${COIN_EMOJI} suficientes! Precisa de ${finalPrice}, mas tem apenas ${playerData.superlockpoints}.`,
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        // Deduzir SuperLockPoints
        addSuperlockPoints(userId, -finalPrice);

        // Adicionar Box ao inventário do jogador
        const { addBoxToPlayer } = require('../utils/database');
        const boxId = addBoxToPlayer(userId);

        // Criar embed de compra
        let priceDisplay = `${finalPrice} ${COIN_EMOJI}`;
        if (discount > 0) {
            priceDisplay += ` ~~${boxBasePrice}~~ *(-${discount} VIP)*`;
        }

        const purchaseEmbed = new EmbedBuilder()
            .setTitle('📦 Mystery Box Adquirida!')
            .setDescription(`**Parabéns!** Você comprou uma Mystery Box!\n\n**Preço:** ${priceDisplay}\n**Conteúdo:** 3 cartas aleatórias`)
            .setColor(0x9B59B6)
            .addFields(
                {
                    name: '💳 Comprador',
                    value: `${interaction.user.username}`,
                    inline: true
                },
                {
                    name: `${COIN_EMOJI} Saldo Restante`,
                    value: `${playerData.superlockpoints - finalPrice} SuperLockPoints`,
                    inline: true
                },
                {
                    name: '📦 Como Abrir',
                    value: `Use \`/box\` para gerenciar suas boxes!\n**ID da Box:** ${boxId}`,
                    inline: false
                }
            )
            .setFooter({
                text: `Comprado por ${interaction.user.username} • Use /box`,
                iconURL: interaction.user.displayAvatarURL()
            })
            .setTimestamp();

        // Verificar se é um botão ou comando (sempre limpar imagens)
        const replyOptions = { embeds: [purchaseEmbed], components: [], files: [] };

        if (interaction.isButton()) {
            await interaction.update(replyOptions);
        } else {
            await interaction.reply(replyOptions);
        }
    }
};
