const { <PERSON>lash<PERSON><PERSON>mand<PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>er, ActionRowBuilder, ButtonBuilder, ButtonStyle, AttachmentBuilder } = require('discord.js');
const { getPlayerData, getPlayerBoxes, openBox, claimBoxCard, removeBox, autoOpenExpiredBoxes, addSuperlockPoints } = require('../utils/database');
const { RARITIES, COIN_EMOJI } = require('../config/cards');
const { applyVipDiscount, getPlayerVip } = require('../config/vip');
const fs = require('fs');
const path = require('path');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('box')
        .setDescription('📦 Sistema completo de Mystery Boxes!')
        .addStringOption(option =>
            option.setName('acao')
                .setDescription('O que você quer fazer?')
                .setRequired(false)
                .addChoices(
                    { name: '📋 Ver minhas boxes', value: 'list' },
                    { name: '📦 Comprar box', value: 'buy' },
                    { name: '🎁 Abrir todas', value: 'openall' }
                ))
        .addStringOption(option =>
            option.setName('box_id')
                .setDescription('ID da box para abrir (opcional)')
                .setRequired(false)),

    async execute(interaction) {
        const userId = interaction.user.id;
        const acao = interaction.options.getString('acao') || 'list';
        const boxId = interaction.options.getString('box_id');

        // Verificar boxes expiradas primeiro
        autoOpenExpiredBoxes(userId);

        if (acao === 'list') {
            if (boxId) {
                await this.openSpecificBox(interaction, boxId);
            } else {
                await this.showBoxList(interaction);
            }
        } else if (acao === 'buy') {
            await this.buyBox(interaction);
        } else if (acao === 'openall') {
            await this.openAllBoxes(interaction);
        }
    },

    async showBoxList(interaction) {
        const userId = interaction.user.id;
        const playerData = getPlayerData(userId);
        const boxes = getPlayerBoxes(userId);

        if (boxes.length === 0) {
            const noBoxesEmbed = new EmbedBuilder()
                .setTitle('📦 Mystery Boxes')
                .setDescription(`💰 **Saldo:** ${playerData.superlockpoints} ${COIN_EMOJI}\n\n❌ **Você não possui boxes!**\n\n🛒 Use \`/box acao:📦 Comprar box\` para adquirir uma Mystery Box!`)
                .setColor(0x95A5A6)
                .setThumbnail('https://cdn.discordapp.com/emojis/📦.png')
                .setFooter({ 
                    text: 'MeGa Football Community - Box System', 
                    iconURL: interaction.user.displayAvatarURL() 
                });

            const buyButton = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('box_buy_new')
                        .setLabel('Comprar Mystery Box')
                        .setStyle(ButtonStyle.Success)
                );

            await interaction.reply({ embeds: [noBoxesEmbed], components: [buyButton] });
            return;
        }

        const closedBoxes = boxes.filter(box => !box.opened);
        const openedBoxes = boxes.filter(box => box.opened);
        const unclaimedBoxes = openedBoxes.filter(box => box.cards.some(card => !card.claimed));

        const boxEmbed = new EmbedBuilder()
            .setTitle('📦 Suas Mystery Boxes')
            .setDescription(`💰 **Saldo:** ${playerData.superlockpoints} ${COIN_EMOJI}\n\n📊 **Estatísticas:**\n📦 Total: ${boxes.length} | 🔒 Fechadas: ${closedBoxes.length} | 📂 Abertas: ${openedBoxes.length} | 🎁 Pendentes: ${unclaimedBoxes.length}`)
            .setColor(0x9B59B6)
            .setThumbnail(interaction.user.displayAvatarURL())
            .setFooter({ 
                text: 'Boxes auto-abrem em 2 minutos • Clique para interagir', 
                iconURL: interaction.client.user.displayAvatarURL() 
            });

        // Mostrar boxes fechadas com timer
        if (closedBoxes.length > 0) {
            const closedList = closedBoxes.slice(0, 3).map(box => {
                const timeLeft = Math.max(0, box.autoOpenTime - Date.now());
                const minutesLeft = Math.floor(timeLeft / 60000);
                const secondsLeft = Math.floor((timeLeft % 60000) / 1000);
                
                return `🔒 \`${box.id.slice(-8)}\` - ⏰ ${minutesLeft}m ${secondsLeft}s`;
            }).join('\n');

            boxEmbed.addFields({
                name: '🔒 Boxes Fechadas',
                value: closedList + (closedBoxes.length > 3 ? `\n*...e mais ${closedBoxes.length - 3}*` : ''),
                inline: false
            });
        }

        // Mostrar boxes com cartas pendentes
        if (unclaimedBoxes.length > 0) {
            const unclaimedList = unclaimedBoxes.slice(0, 3).map(box => {
                const pendingCards = box.cards.filter(card => !card.claimed).length;
                return `🎁 \`${box.id.slice(-8)}\` - ${pendingCards} carta(s) para coletar`;
            }).join('\n');

            boxEmbed.addFields({
                name: '🎁 Cartas Pendentes',
                value: unclaimedList + (unclaimedBoxes.length > 3 ? `\n*...e mais ${unclaimedBoxes.length - 3}*` : ''),
                inline: false
            });
        }

        // Criar botões dinâmicos
        const components = [];
        
        const mainRow = new ActionRowBuilder();
        
        // Botão de comprar sempre disponível
        mainRow.addComponents(
            new ButtonBuilder()
                .setCustomId('box_buy_new')
                .setLabel('Comprar Box')
                .setStyle(ButtonStyle.Success)
        );

        if (closedBoxes.length > 0) {
            mainRow.addComponents(
                new ButtonBuilder()
                    .setCustomId('box_open_first')
                    .setLabel(`Abrir Primeira (${closedBoxes.length})`)
                    .setStyle(ButtonStyle.Primary)
            );
        }

        if (closedBoxes.length > 1) {
            mainRow.addComponents(
                new ButtonBuilder()
                    .setCustomId('box_open_all')
                    .setLabel(`Abrir Todas (${closedBoxes.length})`)
                    .setStyle(ButtonStyle.Secondary)
            );
        }

        components.push(mainRow);

        // Segunda linha para boxes com cartas pendentes
        if (unclaimedBoxes.length > 0) {
            const claimRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('box_claim_first')
                        .setLabel(`Ver Cartas Pendentes (${unclaimedBoxes.length})`)
                        .setStyle(ButtonStyle.Secondary)
                );
            components.push(claimRow);
        }

        // SEMPRE limpar imagens anteriores e garantir que não há anexos
        const replyOptions = {
            embeds: [boxEmbed],
            components,
            files: [], // Limpar arquivos
            attachments: [] // Limpar anexos
        };

        if (interaction.isButton()) {
            await interaction.update(replyOptions);
        } else {
            await interaction.reply(replyOptions);
        }
    },

    async buyBox(interaction) {
        const userId = interaction.user.id;
        const playerData = getPlayerData(userId);
        const vipData = getPlayerVip(userId);
        
        // Obter preço da box da loja
        const { getBoxPrice } = require('./shop');
        const boxBasePrice = getBoxPrice();
        const finalPrice = applyVipDiscount(boxBasePrice, vipData.type, 'pack');
        const discount = boxBasePrice - finalPrice;
        
        if (playerData.superlockpoints < finalPrice) {
            await interaction.reply({
                content: `❌ **Saldo insuficiente!**\n\n💰 Você tem: ${playerData.superlockpoints} ${COIN_EMOJI}\n💸 Precisa de: ${finalPrice} ${COIN_EMOJI}\n❗ Faltam: ${finalPrice - playerData.superlockpoints} ${COIN_EMOJI}`,
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        // Deduzir dinheiro e adicionar box
        addSuperlockPoints(userId, -finalPrice);
        const { addBoxToPlayer } = require('../utils/database');
        const boxId = addBoxToPlayer(userId);
        
        // Criar embed de compra
        let priceDisplay = `${finalPrice} ${COIN_EMOJI}`;
        if (discount > 0) {
            priceDisplay += ` ~~${boxBasePrice}~~ *(-${discount} VIP)*`;
        }
        
        const purchaseEmbed = new EmbedBuilder()
            .setTitle('🎉 Mystery Box Adquirida!')
            .setDescription(`**Parabéns!** Você comprou uma Mystery Box!\n\n📦 **Box ID:** \`${boxId.slice(-8)}\`\n💰 **Preço:** ${priceDisplay}\n🎴 **Conteúdo:** 3 cartas aleatórias`)
            .setColor(0x00FF00)
            .addFields(
                {
                    name: '💳 Comprador',
                    value: `${interaction.user.username}`,
                    inline: true
                },
                {
                    name: `${COIN_EMOJI} Saldo Restante`,
                    value: `${playerData.superlockpoints - finalPrice}`,
                    inline: true
                },
                {
                    name: '⏰ Auto-Abertura',
                    value: 'Em 2 minutos se não abrir',
                    inline: true
                }
            )
            .setThumbnail('https://cdn.discordapp.com/emojis/📦.png')
            .setFooter({ 
                text: `Comprado por ${interaction.user.username} • Use /box para gerenciar`, 
                iconURL: interaction.user.displayAvatarURL() 
            })
            .setTimestamp();

        const actionRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`box_open_${boxId}`)
                    .setLabel('Abrir Agora!')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('box_list_all')
                    .setLabel('Ver Todas as Boxes')
                    .setStyle(ButtonStyle.Secondary)
            );

        // SEMPRE limpar imagens anteriores e garantir que não há anexos
        const replyOptions = {
            embeds: [purchaseEmbed],
            components: [actionRow],
            files: [], // Limpar arquivos
            attachments: [] // Limpar anexos
        };

        if (interaction.isButton()) {
            await interaction.update(replyOptions);
        } else {
            await interaction.reply(replyOptions);
        }
    },

    async openSpecificBox(interaction, boxId) {
        const userId = interaction.user.id;
        const boxes = getPlayerBoxes(userId);
        
        // Encontrar box pelo ID (busca parcial)
        const box = boxes.find(b => b.id.includes(boxId) || b.id.slice(-8) === boxId);
        
        if (!box) {
            await interaction.reply({
                content: `❌ **Box não encontrada!**\n\n🔍 ID buscado: \`${boxId}\`\n💡 Use \`/box\` para ver suas boxes disponíveis.`,
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        if (box.opened) {
            await this.showBoxCards(interaction, box, 0);
        } else {
            await this.openAndShowBox(interaction, box);
        }
    },

    async openAndShowBox(interaction, box) {
        const userId = interaction.user.id;
        const openedBox = openBox(userId, box.id);
        
        if (!openedBox) {
            await interaction.reply({
                content: '❌ **Erro ao abrir a box!**\n\nTente novamente em alguns segundos.',
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        await this.showBoxCards(interaction, openedBox, 0);
    },

    async showBoxCards(interaction, box, cardIndex) {
        if (cardIndex >= box.cards.length) {
            await this.showBoxComplete(interaction, box);
            return;
        }

        const card = box.cards[cardIndex];
        const rarityInfo = RARITIES[card.rarity];
        const isLastCard = cardIndex === box.cards.length - 1;
        const isFirstCard = cardIndex === 0;

        const cardEmbed = new EmbedBuilder()
            .setTitle(`✨ Mystery Box - Carta ${cardIndex + 1}/3`)
            .setDescription(`🎴 **${card.name}**\n${rarityInfo.emoji} **${rarityInfo.name}**\n\n📦 **Box:** \`${box.id.slice(-8)}\`\n💰 **Valor:** ${rarityInfo.price} ${COIN_EMOJI}`)
            .setColor(rarityInfo.color)
            .addFields(
                {
                    name: '📊 Status da Carta',
                    value: card.claimed ? '✅ **Coletada**' : '⏳ **Aguardando Coleta**',
                    inline: true
                },
                {
                    name: '🎯 Progresso',
                    value: `Carta ${cardIndex + 1} de 3`,
                    inline: true
                },
                {
                    name: '💎 Raridade',
                    value: `${rarityInfo.emoji} ${rarityInfo.name}`,
                    inline: true
                }
            )
            .setFooter({ 
                text: isLastCard ? '🎉 Última carta da box!' : `➡️ Próxima: Carta ${cardIndex + 2}/3`, 
                iconURL: interaction.client.user.displayAvatarURL() 
            })
            .setTimestamp();

        // Tentar adicionar imagem da carta
        const imagePath = path.join(__dirname, '..', 'images', `${card.name}.jpg`);
        let attachment = null;
        let hasImage = false;

        if (fs.existsSync(imagePath)) {
            try {
                attachment = new AttachmentBuilder(imagePath, { name: `${card.name}.jpg` });
                cardEmbed.setImage(`attachment://${card.name}.jpg`);
                hasImage = true;
            } catch (error) {
                console.log(`Erro ao carregar imagem para ${card.name}:`, error.message);
                cardEmbed.setThumbnail(interaction.user.displayAvatarURL());
            }
        } else {
            // Usar thumbnail do usuário se não houver imagem
            cardEmbed.setThumbnail(interaction.user.displayAvatarURL());
        }

        // Criar botões de navegação e ação
        const components = [];
        
        // Linha 1: Navegação
        const navRow = new ActionRowBuilder();
        
        if (!isFirstCard) {
            navRow.addComponents(
                new ButtonBuilder()
                    .setCustomId(`box_prev_${box.id}_${cardIndex - 1}`)
                    .setLabel('← Anterior')
                    .setStyle(ButtonStyle.Secondary)
            );
        }

        if (!isLastCard) {
            navRow.addComponents(
                new ButtonBuilder()
                    .setCustomId(`box_next_${box.id}_${cardIndex + 1}`)
                    .setLabel('Próxima →')
                    .setStyle(ButtonStyle.Primary)
            );
        } else {
            navRow.addComponents(
                new ButtonBuilder()
                    .setCustomId(`box_finish_${box.id}`)
                    .setLabel('🏁 Finalizar Box')
                    .setStyle(ButtonStyle.Success)
            );
        }

        if (navRow.components.length > 0) {
            components.push(navRow);
        }

        // Linha 2: Ações da carta
        const actionRow = new ActionRowBuilder();
        
        if (!card.claimed) {
            actionRow.addComponents(
                new ButtonBuilder()
                    .setCustomId(`box_claim_${box.id}_${cardIndex}`)
                    .setLabel(`🎁 Coletar ${card.name}`)
                    .setStyle(ButtonStyle.Success)
            );
        }

        actionRow.addComponents(
            new ButtonBuilder()
                .setCustomId('box_list_all')
                .setLabel('📋 Ver Todas as Boxes')
                .setStyle(ButtonStyle.Secondary)
        );

        if (actionRow.components.length > 0) {
            components.push(actionRow);
        }

        // Preparar opções de resposta com limpeza de imagens anteriores
        const replyOptions = {
            embeds: [cardEmbed],
            components,
            files: [] // Sempre limpar arquivos anteriores
        };

        // Adicionar nova imagem apenas se existir
        if (attachment && hasImage) {
            replyOptions.files = [attachment];
        }

        // Verificar se é uma navegação (botão clicado) ou comando inicial
        if (interaction.isButton()) {
            await interaction.update(replyOptions);
        } else if (interaction.replied || interaction.deferred) {
            await interaction.editReply(replyOptions);
        } else {
            await interaction.reply(replyOptions);
        }
    },

    async showBoxComplete(interaction, box) {
        const completedCards = box.cards.filter(card => card.claimed).length;
        const totalValue = box.cards.reduce((sum, card) => {
            if (card.claimed) {
                return sum + RARITIES[card.rarity].price;
            }
            return sum;
        }, 0);

        const completeEmbed = new EmbedBuilder()
            .setTitle('🎉 Mystery Box Completa!')
            .setDescription(`**Box ID:** \`${box.id.slice(-8)}\`\n\n🎴 **Cartas Coletadas:** ${completedCards}/3\n💰 **Valor Total:** ${totalValue} ${COIN_EMOJI}`)
            .setColor(0x00FF00)
            .addFields(
                {
                    name: '🎴 Resumo das Cartas',
                    value: box.cards.map((card, index) => {
                        const rarityInfo = RARITIES[card.rarity];
                        const status = card.claimed ? '✅' : '❌';
                        return `${status} ${rarityInfo.emoji} **${card.name}** (${rarityInfo.price} ${COIN_EMOJI})`;
                    }).join('\n'),
                    inline: false
                }
            )
            .setThumbnail(interaction.user.displayAvatarURL())
            .setFooter({
                text: `Box completa por ${interaction.user.username}`,
                iconURL: interaction.user.displayAvatarURL()
            })
            .setTimestamp();

        const actionRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('box_list_all')
                    .setLabel('📋 Ver Todas as Boxes')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('box_buy_new')
                    .setLabel('🛒 Comprar Nova Box')
                    .setStyle(ButtonStyle.Success)
            );

        // SEMPRE limpar imagens anteriores e garantir que não há anexos
        const replyOptions = {
            embeds: [completeEmbed],
            components: [actionRow],
            files: [], // Limpar arquivos
            attachments: [] // Limpar anexos
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.editReply(replyOptions);
        } else {
            await interaction.reply(replyOptions);
        }
    },

    async openAllBoxes(interaction) {
        const userId = interaction.user.id;
        const boxes = getPlayerBoxes(userId);
        const closedBoxes = boxes.filter(box => !box.opened);

        if (closedBoxes.length === 0) {
            await interaction.reply({
                content: '❌ **Nenhuma box fechada!**\n\n💡 Use \`/box acao:📦 Comprar box\` para adquirir novas Mystery Boxes.',
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        // Abrir todas as boxes e auto-coletar
        let totalCards = 0;
        const rarityCount = {};

        for (const box of closedBoxes) {
            const openedBox = openBox(userId, box.id);
            if (openedBox) {
                totalCards += openedBox.cards.length;
                
                // Auto-coletar todas as cartas
                openedBox.cards.forEach((card, index) => {
                    claimBoxCard(userId, box.id, index);
                    
                    if (!rarityCount[card.rarity]) {
                        rarityCount[card.rarity] = 0;
                    }
                    rarityCount[card.rarity]++;
                });
            }
        }

        // Criar embed de resultado
        const resultEmbed = new EmbedBuilder()
            .setTitle('🎉 Todas as Boxes Abertas!')
            .setDescription(`**Boxes Abertas:** ${closedBoxes.length}\n**Total de Cartas:** ${totalCards}\n\n🎁 **Todas as cartas foram coletadas automaticamente!**`)
            .setColor(0x00FF00)
            .setThumbnail(interaction.user.displayAvatarURL())
            .setFooter({ 
                text: `Abertas por ${interaction.user.username}`, 
                iconURL: interaction.user.displayAvatarURL() 
            })
            .setTimestamp();

        // Mostrar distribuição de raridades
        if (Object.keys(rarityCount).length > 0) {
            const rarityText = Object.entries(rarityCount)
                .sort(([,a], [,b]) => b - a) // Ordenar por quantidade
                .map(([rarity, count]) => {
                    const rarityInfo = RARITIES[rarity];
                    return `${rarityInfo.emoji} **${rarityInfo.name}:** ${count}x (${count * rarityInfo.price} ${COIN_EMOJI})`;
                })
                .join('\n');

            resultEmbed.addFields({
                name: '🎴 Cartas Obtidas',
                value: rarityText,
                inline: false
            });
        }

        const actionRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('box_list_all')
                    .setLabel('📋 Ver Todas as Boxes')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('box_buy_new')
                    .setLabel('🛒 Comprar Nova Box')
                    .setStyle(ButtonStyle.Success)
            );

        // SEMPRE limpar imagens anteriores e garantir que não há anexos
        const replyOptions = {
            embeds: [resultEmbed],
            components: [actionRow],
            files: [], // Limpar arquivos
            attachments: [] // Limpar anexos
        };

        if (interaction.isButton()) {
            await interaction.update(replyOptions);
        } else {
            await interaction.reply(replyOptions);
        }
    },

    async handleBoxButtons(interaction) {
        const userId = interaction.user.id;
        
        // Verificar se é o usuário que executou o comando original
        const originalUserId = interaction.message.interaction?.user?.id;
        if (originalUserId && userId !== originalUserId) {
            await interaction.reply({
                content: '❌ Apenas quem executou o comando pode interagir com ele!',
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        const customId = interaction.customId;

        if (customId === 'box_buy_new') {
            await this.buyBox(interaction);
        } else if (customId === 'box_list_all') {
            await this.showBoxList(interaction);
        } else if (customId === 'box_open_first') {
            const boxes = getPlayerBoxes(userId);
            const firstClosedBox = boxes.find(box => !box.opened);

            if (firstClosedBox) {
                await this.openAndShowBox(interaction, firstClosedBox);
            } else {
                await interaction.reply({
                    content: '❌ **Nenhuma box fechada encontrada!**',
                    flags: 64 // MessageFlags.Ephemeral
                });
            }
        } else if (customId === 'box_open_all') {
            await this.openAllBoxes(interaction);
        } else if (customId === 'box_claim_first') {
            // Mostrar primeira box com cartas pendentes
            const boxes = getPlayerBoxes(userId);
            const firstUnclaimedBox = boxes.find(box => box.opened && box.cards.some(card => !card.claimed));

            if (firstUnclaimedBox) {
                // Encontrar primeira carta não coletada
                const firstUnclaimedCardIndex = firstUnclaimedBox.cards.findIndex(card => !card.claimed);
                await this.showBoxCards(interaction, firstUnclaimedBox, firstUnclaimedCardIndex);
            } else {
                await interaction.reply({
                    content: '❌ **Nenhuma carta pendente encontrada!**',
                    flags: 64 // MessageFlags.Ephemeral
                });
            }
        } else if (customId.startsWith('box_open_')) {
            const boxId = customId.split('_')[2];
            const boxes = getPlayerBoxes(userId);
            const box = boxes.find(b => b.id === boxId);
            if (box) {
                await this.openAndShowBox(interaction, box);
            }
        } else if (customId.startsWith('box_claim_')) {
            const parts = customId.split('_');
            if (parts.length === 4) {
                const boxId = parts[2];
                const cardIndex = parseInt(parts[3]);

                const claimedCard = claimBoxCard(userId, boxId, cardIndex);
                if (claimedCard) {
                    const boxes = getPlayerBoxes(userId);
                    const box = boxes.find(b => b.id === boxId);

                    // Mostrar feedback de coleta
                    const rarityInfo = RARITIES[claimedCard.rarity];
                    const collectEmbed = new EmbedBuilder()
                        .setTitle('🎁 Carta Coletada!')
                        .setDescription(`**${claimedCard.name}** foi adicionada ao seu inventário!\n\n${rarityInfo.emoji} **${rarityInfo.name}**\n💰 **Valor:** ${rarityInfo.price} ${COIN_EMOJI}\n\n🆔 **ID da Carta:** ${claimedCard.id}`)
                        .setColor(rarityInfo.color)
                        .setThumbnail(interaction.user.displayAvatarURL())
                        .setFooter({
                            text: 'Carta coletada com sucesso!',
                            iconURL: interaction.user.displayAvatarURL()
                        });

                    const continueRow = new ActionRowBuilder()
                        .addComponents(
                            new ButtonBuilder()
                                .setCustomId(`box_next_${box.id}_${cardIndex + 1}`)
                                .setLabel('Próxima Carta →')
                                .setStyle(ButtonStyle.Primary)
                                .setDisabled(cardIndex + 1 >= box.cards.length),
                            new ButtonBuilder()
                                .setCustomId(`box_finish_${box.id}`)
                                .setLabel('🏁 Finalizar Box')
                                .setStyle(ButtonStyle.Success)
                        );

                    // SEMPRE limpar imagens anteriores e garantir que não há anexos
                    const replyOptions = {
                        embeds: [collectEmbed],
                        components: [continueRow],
                        files: [], // Limpar arquivos
                        attachments: [] // Limpar anexos
                    };

                    await interaction.update(replyOptions);
                } else {
                    await interaction.reply({
                        content: '❌ **Erro ao coletar carta!** Ela pode já ter sido coletada.',
                        flags: 64 // MessageFlags.Ephemeral
                    });
                }
            }
        } else if (customId.startsWith('box_next_') || customId.startsWith('box_prev_')) {
            const parts = customId.split('_');
            const boxId = parts[2];
            const cardIndex = parseInt(parts[3]);

            const boxes = getPlayerBoxes(userId);
            const box = boxes.find(b => b.id === boxId);
            if (box) {
                await this.showBoxCards(interaction, box, cardIndex);
            }
        } else if (customId.startsWith('box_finish_')) {
            const boxId = customId.split('_')[2];
            const boxes = getPlayerBoxes(userId);
            const box = boxes.find(b => b.id === boxId);
            if (box) {
                await this.showBoxComplete(interaction, box);
            }
        }
    }
};
